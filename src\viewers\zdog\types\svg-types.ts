/**
 * TypeScript type definitions for SVG parsing and processing
 * Comprehensive type safety for SVG elements and path commands
 */

// SVG Path command types
export type SVGPathCommandType = 'M' | 'L' | 'H' | 'V' | 'C' | 'S' | 'Q' | 'T' | 'A' | 'Z' | 
                                 'm' | 'l' | 'h' | 'v' | 'c' | 's' | 'q' | 't' | 'a' | 'z';

// Individual path command interface
export interface SVGPathCommand {
  type: SVGPathCommandType;
  points: number[];
  absolute: boolean;
  original?: string; // Original command string for debugging
}

// Arc command specific parameters
export interface SVGArcCommand extends SVGPathCommand {
  type: 'A' | 'a';
  rx: number;
  ry: number;
  xAxisRotation: number;
  largeArcFlag: boolean;
  sweepFlag: boolean;
  x: number;
  y: number;
}

// Bezier curve command parameters
export interface SVGCubicBezierCommand extends SVGPathCommand {
  type: 'C' | 'c';
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  x: number;
  y: number;
}

export interface SVGQuadraticBezierCommand extends SVGPathCommand {
  type: 'Q' | 'q';
  x1: number;
  y1: number;
  x: number;
  y: number;
}

// Union type for all command types
export type SVGPathCommandUnion = SVGPathCommand | SVGArcCommand | SVGCubicBezierCommand | SVGQuadraticBezierCommand;

// SVG element types
export type SVGElementType = 'path' | 'circle' | 'rect' | 'ellipse' | 'polygon' | 'polyline' | 'line' | 'g';

// Parsed SVG element interface
export interface ParsedSVGElement {
  type: SVGElementType;
  id?: string;
  attributes: Record<string, string>;
  transform?: DOMMatrix;
  style?: Partial<CSSStyleDeclaration>;
  children?: ParsedSVGElement[];
  pathCommands?: SVGPathCommandUnion[];
}

// SVG style properties relevant to 3D rendering
export interface SVGStyle {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  strokeLinecap?: 'butt' | 'round' | 'square';
  strokeLinejoin?: 'miter' | 'round' | 'bevel';
  strokeDasharray?: string;
  strokeDashoffset?: number;
  opacity?: number;
  fillOpacity?: number;
  strokeOpacity?: number;
  visibility?: 'visible' | 'hidden' | 'collapse';
  display?: string;
}

// Transform matrix interface
export interface SVGTransform {
  type: 'matrix' | 'translate' | 'scale' | 'rotate' | 'skewX' | 'skewY';
  values: number[];
}

// SVG viewBox interface
export interface SVGViewBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

// SVG document interface
export interface ParsedSVGDocument {
  width?: number;
  height?: number;
  viewBox?: SVGViewBox;
  elements: ParsedSVGElement[];
  metadata?: {
    title?: string;
    description?: string;
    creator?: string;
  };
}

// Error types for SVG parsing
export class SVGParseError extends Error {
  constructor(
    message: string,
    public line?: number,
    public column?: number,
    public elementType?: string,
    public attributeName?: string
  ) {
    super(message);
    this.name = 'SVGParseError';
  }
}

export class SVGPathParseError extends SVGParseError {
  constructor(
    message: string,
    public pathData?: string,
    public commandIndex?: number,
    line?: number,
    column?: number
  ) {
    super(message, line, column, 'path', 'd');
    this.name = 'SVGPathParseError';
  }
}

export class SVGTransformParseError extends SVGParseError {
  constructor(
    message: string,
    public transformData?: string,
    line?: number,
    column?: number
  ) {
    super(message, line, column, undefined, 'transform');
    this.name = 'SVGTransformParseError';
  }
}

// Parser configuration options
export interface SVGParserOptions {
  preserveAspectRatio?: boolean;
  ignoreInvalidCommands?: boolean;
  simplifyPaths?: boolean;
  convertToAbsolute?: boolean;
  precision?: number;
  validateCommands?: boolean;
}

// Path simplification options
export interface PathSimplificationOptions {
  tolerance?: number;
  removeRedundantPoints?: boolean;
  mergeConsecutiveLines?: boolean;
  convertCurvesToLines?: boolean;
  curveResolution?: number;
}

// Coordinate system conversion options
export interface CoordinateSystemOptions {
  flipY?: boolean;
  centerOrigin?: boolean;
  scale?: number;
  translateX?: number;
  translateY?: number;
}

// SVG to Zdog conversion context
export interface SVGToZdogContext {
  currentPosition: { x: number; y: number };
  currentTransform: DOMMatrix;
  styleStack: SVGStyle[];
  groupStack: string[];
  pathIndex: number;
  elementIndex: number;
}

// Validation result interface
export interface SVGValidationResult {
  isValid: boolean;
  errors: SVGParseError[];
  warnings: string[];
  elementCount: number;
  pathCommandCount: number;
}

// Export utility type for path command validation
export type PathCommandValidator = (command: SVGPathCommandUnion) => boolean;

// Export utility type for element validation
export type ElementValidator = (element: ParsedSVGElement) => SVGValidationResult;
