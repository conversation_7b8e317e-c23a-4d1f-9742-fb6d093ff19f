/**
 * SVG Parser with comprehensive TypeScript support
 * Handles SVG path commands, elements, transforms, and styles
 */

import type {
  SVGPathCommand,
  SVGPathCommandType,
  SVGPathCommandUnion,
  SVGArcCommand,
  SVGCubicBezierCommand,
  SVGQuadraticBezierCommand,
  ParsedSVGElement,
  ParsedSVGDocument,
  SVGStyle,
  SVGTransform,
  SVGViewBox,
  SVGParseError,
  SVGPathParseError,
  SVGTransformParseError,
  SVGParserOptions,
  PathSimplificationOptions,
  SVGValidationResult,
  SVGToZdogContext
} from './types/svg-types';

export class SVGParser {
  private options: Required<SVGParserOptions>;
  private context: SVGToZdogContext;

  constructor(options: SVGParserOptions = {}) {
    this.options = {
      preserveAspectRatio: options.preserveAspectRatio ?? true,
      ignoreInvalidCommands: options.ignoreInvalidCommands ?? false,
      simplifyPaths: options.simplifyPaths ?? false,
      convertToAbsolute: options.convertToAbsolute ?? true,
      precision: options.precision ?? 3,
      validateCommands: options.validateCommands ?? true
    };

    this.context = {
      currentPosition: { x: 0, y: 0 },
      currentTransform: new DOMMatrix(),
      styleStack: [],
      groupStack: [],
      pathIndex: 0,
      elementIndex: 0
    };
  }

  /**
   * Parse SVG content string into structured document
   */
  public parseSVG(svgContent: string): ParsedSVGDocument {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(svgContent, 'image/svg+xml');
      
      // Check for parsing errors
      const parserError = doc.querySelector('parsererror');
      if (parserError) {
        throw new SVGParseError('Invalid SVG markup', undefined, undefined, 'document');
      }

      const svgElement = doc.querySelector('svg');
      if (!svgElement) {
        throw new SVGParseError('No SVG root element found');
      }

      return this.parseSVGElement(svgElement);
    } catch (error) {
      if (error instanceof SVGParseError) {
        throw error;
      }
      throw new SVGParseError(`Failed to parse SVG: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse SVG root element into document structure
   */
  private parseSVGElement(svgElement: SVGSVGElement): ParsedSVGDocument {
    const width = this.parseNumber(svgElement.getAttribute('width'));
    const height = this.parseNumber(svgElement.getAttribute('height'));
    const viewBox = this.parseViewBox(svgElement.getAttribute('viewBox'));

    const elements: ParsedSVGElement[] = [];
    
    // Parse child elements
    for (const child of Array.from(svgElement.children)) {
      try {
        const element = this.parseElement(child as SVGElement);
        if (element) {
          elements.push(element);
        }
      } catch (error) {
        if (!this.options.ignoreInvalidCommands) {
          throw error;
        }
        console.warn('Skipping invalid element:', error);
      }
    }

    return {
      width,
      height,
      viewBox,
      elements,
      metadata: this.parseMetadata(svgElement)
    };
  }

  /**
   * Parse individual SVG element
   */
  private parseElement(element: SVGElement): ParsedSVGElement | null {
    const tagName = element.tagName.toLowerCase();
    
    if (!this.isSupportedElement(tagName)) {
      if (!this.options.ignoreInvalidCommands) {
        throw new SVGParseError(`Unsupported element type: ${tagName}`);
      }
      return null;
    }

    this.context.elementIndex++;

    const parsed: ParsedSVGElement = {
      type: tagName as any,
      id: element.id || `element_${this.context.elementIndex}`,
      attributes: this.parseAttributes(element),
      style: this.parseStyle(element),
      transform: this.parseTransform(element.getAttribute('transform'))
    };

    // Parse element-specific data
    switch (tagName) {
      case 'path':
        parsed.pathCommands = this.parsePath(element.getAttribute('d') || '');
        break;
      case 'g':
        parsed.children = this.parseGroupChildren(element);
        break;
      default:
        // Handle other shape elements (circle, rect, etc.)
        break;
    }

    return parsed;
  }

  /**
   * Parse SVG path data into command array
   */
  public parsePath(pathData: string): SVGPathCommandUnion[] {
    if (!pathData.trim()) {
      return [];
    }

    try {
      this.context.pathIndex++;
      const commands: SVGPathCommandUnion[] = [];
      
      // Normalize path data - add spaces around commands
      const normalized = pathData
        .replace(/([MmLlHhVvCcSsQqTtAaZz])/g, ' $1 ')
        .replace(/,/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      const tokens = normalized.split(' ').filter(token => token.length > 0);
      let i = 0;

      while (i < tokens.length) {
        const commandChar = tokens[i];
        
        if (!this.isValidCommand(commandChar)) {
          if (!this.options.ignoreInvalidCommands) {
            throw new SVGPathParseError(`Invalid path command: ${commandChar}`, pathData, i);
          }
          i++;
          continue;
        }

        const command = this.parsePathCommand(commandChar, tokens, i);
        if (command) {
          commands.push(command);
          i += this.getCommandParameterCount(commandChar) + 1;
        } else {
          i++;
        }
      }

      return this.options.convertToAbsolute ? this.convertToAbsolute(commands) : commands;
    } catch (error) {
      if (error instanceof SVGPathParseError) {
        throw error;
      }
      throw new SVGPathParseError(`Failed to parse path: ${error instanceof Error ? error.message : 'Unknown error'}`, pathData);
    }
  }

  /**
   * Parse individual path command
   */
  private parsePathCommand(commandChar: string, tokens: string[], startIndex: number): SVGPathCommandUnion | null {
    const type = commandChar as SVGPathCommandType;
    const absolute = type === type.toUpperCase();
    const paramCount = this.getCommandParameterCount(commandChar);
    
    if (startIndex + paramCount >= tokens.length) {
      if (!this.options.ignoreInvalidCommands) {
        throw new SVGPathParseError(`Insufficient parameters for command ${commandChar}`);
      }
      return null;
    }

    const points: number[] = [];
    for (let i = 1; i <= paramCount; i++) {
      const value = parseFloat(tokens[startIndex + i]);
      if (isNaN(value)) {
        if (!this.options.ignoreInvalidCommands) {
          throw new SVGPathParseError(`Invalid numeric parameter: ${tokens[startIndex + i]}`);
        }
        return null;
      }
      points.push(this.roundToPrecision(value));
    }

    const baseCommand: SVGPathCommand = {
      type,
      points,
      absolute,
      original: tokens.slice(startIndex, startIndex + paramCount + 1).join(' ')
    };

    // Create specialized command objects for complex commands
    switch (type.toLowerCase()) {
      case 'a':
        return this.createArcCommand(baseCommand);
      case 'c':
        return this.createCubicBezierCommand(baseCommand);
      case 'q':
        return this.createQuadraticBezierCommand(baseCommand);
      default:
        return baseCommand;
    }
  }

  /**
   * Create arc command with proper typing
   */
  private createArcCommand(base: SVGPathCommand): SVGArcCommand {
    if (base.points.length < 7) {
      throw new SVGPathParseError('Arc command requires 7 parameters');
    }

    return {
      ...base,
      type: base.type as 'A' | 'a',
      rx: base.points[0],
      ry: base.points[1],
      xAxisRotation: base.points[2],
      largeArcFlag: base.points[3] !== 0,
      sweepFlag: base.points[4] !== 0,
      x: base.points[5],
      y: base.points[6]
    };
  }

  /**
   * Create cubic bezier command with proper typing
   */
  private createCubicBezierCommand(base: SVGPathCommand): SVGCubicBezierCommand {
    if (base.points.length < 6) {
      throw new SVGPathParseError('Cubic bezier command requires 6 parameters');
    }

    return {
      ...base,
      type: base.type as 'C' | 'c',
      x1: base.points[0],
      y1: base.points[1],
      x2: base.points[2],
      y2: base.points[3],
      x: base.points[4],
      y: base.points[5]
    };
  }

  /**
   * Create quadratic bezier command with proper typing
   */
  private createQuadraticBezierCommand(base: SVGPathCommand): SVGQuadraticBezierCommand {
    if (base.points.length < 4) {
      throw new SVGPathParseError('Quadratic bezier command requires 4 parameters');
    }

    return {
      ...base,
      type: base.type as 'Q' | 'q',
      x1: base.points[0],
      y1: base.points[1],
      x: base.points[2],
      y: base.points[3]
    };
  }

  /**
   * Convert relative commands to absolute
   */
  private convertToAbsolute(commands: SVGPathCommandUnion[]): SVGPathCommandUnion[] {
    const result: SVGPathCommandUnion[] = [];
    let currentX = 0;
    let currentY = 0;
    let subpathStartX = 0;
    let subpathStartY = 0;

    for (const command of commands) {
      const converted = { ...command };
      
      if (!command.absolute) {
        // Convert relative to absolute based on command type
        switch (command.type.toLowerCase()) {
          case 'm':
            converted.type = 'M';
            converted.points[0] += currentX;
            converted.points[1] += currentY;
            break;
          case 'l':
            converted.type = 'L';
            converted.points[0] += currentX;
            converted.points[1] += currentY;
            break;
          // Add more command conversions as needed
        }
        converted.absolute = true;
      }

      // Update current position
      if (command.type.toLowerCase() !== 'z') {
        const points = converted.points;
        if (points.length >= 2) {
          currentX = points[points.length - 2];
          currentY = points[points.length - 1];
        }
      }

      // Handle move commands for subpath tracking
      if (command.type.toLowerCase() === 'm') {
        subpathStartX = currentX;
        subpathStartY = currentY;
      } else if (command.type.toLowerCase() === 'z') {
        currentX = subpathStartX;
        currentY = subpathStartY;
      }

      result.push(converted);
    }

    return result;
  }

  // Utility methods
  private isValidCommand(char: string): boolean {
    return /^[MmLlHhVvCcSsQqTtAaZz]$/.test(char);
  }

  private isSupportedElement(tagName: string): boolean {
    return ['path', 'circle', 'rect', 'ellipse', 'polygon', 'polyline', 'line', 'g'].includes(tagName);
  }

  private getCommandParameterCount(command: string): number {
    const counts: Record<string, number> = {
      'M': 2, 'm': 2, 'L': 2, 'l': 2, 'H': 1, 'h': 1, 'V': 1, 'v': 1,
      'C': 6, 'c': 6, 'S': 4, 's': 4, 'Q': 4, 'q': 4, 'T': 2, 't': 2,
      'A': 7, 'a': 7, 'Z': 0, 'z': 0
    };
    return counts[command] || 0;
  }

  private parseNumber(value: string | null): number | undefined {
    if (!value) return undefined;
    const num = parseFloat(value);
    return isNaN(num) ? undefined : num;
  }

  private parseViewBox(value: string | null): SVGViewBox | undefined {
    if (!value) return undefined;
    const parts = value.trim().split(/\s+|,/).map(parseFloat);
    if (parts.length !== 4 || parts.some(isNaN)) return undefined;
    return { x: parts[0], y: parts[1], width: parts[2], height: parts[3] };
  }

  private parseAttributes(element: SVGElement): Record<string, string> {
    const attrs: Record<string, string> = {};
    for (const attr of Array.from(element.attributes)) {
      attrs[attr.name] = attr.value;
    }
    return attrs;
  }

  private parseStyle(element: SVGElement): Partial<CSSStyleDeclaration> {
    const style: any = {};
    const styleAttr = element.getAttribute('style');
    
    if (styleAttr) {
      const declarations = styleAttr.split(';');
      for (const decl of declarations) {
        const [property, value] = decl.split(':').map(s => s.trim());
        if (property && value) {
          style[property] = value;
        }
      }
    }

    return style;
  }

  private parseTransform(transformAttr: string | null): DOMMatrix | undefined {
    if (!transformAttr) return undefined;
    
    try {
      // Create a temporary SVG element to parse transform
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
      g.setAttribute('transform', transformAttr);
      svg.appendChild(g);
      
      return new DOMMatrix(getComputedStyle(g).transform);
    } catch (error) {
      throw new SVGTransformParseError(`Invalid transform: ${transformAttr}`);
    }
  }

  private parseGroupChildren(element: SVGElement): ParsedSVGElement[] {
    const children: ParsedSVGElement[] = [];
    
    for (const child of Array.from(element.children)) {
      try {
        const parsed = this.parseElement(child as SVGElement);
        if (parsed) {
          children.push(parsed);
        }
      } catch (error) {
        if (!this.options.ignoreInvalidCommands) {
          throw error;
        }
        console.warn('Skipping invalid child element:', error);
      }
    }

    return children;
  }

  private parseMetadata(svgElement: SVGSVGElement): any {
    const title = svgElement.querySelector('title')?.textContent;
    const description = svgElement.querySelector('desc')?.textContent;
    
    return {
      title: title || undefined,
      description: description || undefined,
      creator: 'Zdog SVG Viewer'
    };
  }

  private roundToPrecision(value: number): number {
    const factor = Math.pow(10, this.options.precision);
    return Math.round(value * factor) / factor;
  }

  /**
   * Validate parsed SVG document
   */
  public validate(document: ParsedSVGDocument): SVGValidationResult {
    const errors: SVGParseError[] = [];
    const warnings: string[] = [];
    let elementCount = 0;
    let pathCommandCount = 0;

    const validateElement = (element: ParsedSVGElement): void => {
      elementCount++;
      
      if (element.pathCommands) {
        pathCommandCount += element.pathCommands.length;
      }
      
      if (element.children) {
        element.children.forEach(validateElement);
      }
    };

    document.elements.forEach(validateElement);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      elementCount,
      pathCommandCount
    };
  }
}
