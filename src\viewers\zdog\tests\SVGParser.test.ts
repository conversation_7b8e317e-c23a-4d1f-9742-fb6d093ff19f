/**
 * Unit tests for SVGParser
 * Tests SVG parsing functionality with TypeScript type safety
 */

import { SVGParser } from '../SVGParser';
import type { 
  SVGPathCommand, 
  ParsedSVGDocument,
  SVGParseError,
  SVGPathParseError 
} from '../types/svg-types';

describe('SVGParser', () => {
  let parser: SVGParser;

  beforeEach(() => {
    parser = new SVGParser();
  });

  describe('Path Command Parsing', () => {
    it('should parse simple move and line commands', () => {
      const pathData = 'M10,10 L20,20';
      const commands = parser.parsePath(pathData);
      
      expect(commands).toHaveLength(2);
      expect(commands[0]).toEqual({
        type: 'M',
        points: [10, 10],
        absolute: true,
        original: expect.any(String)
      });
      expect(commands[1]).toEqual({
        type: 'L',
        points: [20, 20],
        absolute: true,
        original: expect.any(String)
      });
    });

    it('should parse cubic bezier curves', () => {
      const pathData = 'M10,10 C20,20 30,30 40,40';
      const commands = parser.parsePath(pathData);
      
      expect(commands).toHaveLength(2);
      expect(commands[1]).toEqual({
        type: 'C',
        points: [20, 20, 30, 30, 40, 40],
        absolute: true,
        x1: 20,
        y1: 20,
        x2: 30,
        y2: 30,
        x: 40,
        y: 40,
        original: expect.any(String)
      });
    });

    it('should parse arc commands', () => {
      const pathData = 'M10,10 A5,5 0 0,1 20,20';
      const commands = parser.parsePath(pathData);
      
      expect(commands).toHaveLength(2);
      expect(commands[1]).toEqual({
        type: 'A',
        points: [5, 5, 0, 0, 1, 20, 20],
        absolute: true,
        rx: 5,
        ry: 5,
        xAxisRotation: 0,
        largeArcFlag: false,
        sweepFlag: true,
        x: 20,
        y: 20,
        original: expect.any(String)
      });
    });

    it('should handle relative commands', () => {
      const pathData = 'm10,10 l10,10';
      const commands = parser.parsePath(pathData);
      
      expect(commands).toHaveLength(2);
      expect(commands[0].absolute).toBe(false);
      expect(commands[1].absolute).toBe(false);
    });

    it('should convert relative to absolute when configured', () => {
      const parserWithAbsolute = new SVGParser({ convertToAbsolute: true });
      const pathData = 'm10,10 l10,10';
      const commands = parserWithAbsolute.parsePath(pathData);
      
      expect(commands).toHaveLength(2);
      expect(commands[0].absolute).toBe(true);
      expect(commands[1].absolute).toBe(true);
      expect(commands[1].points).toEqual([20, 20]); // 10+10, 10+10
    });

    it('should handle empty path data', () => {
      const commands = parser.parsePath('');
      expect(commands).toHaveLength(0);
    });

    it('should throw error for invalid commands when validation enabled', () => {
      const parserWithValidation = new SVGParser({ 
        validateCommands: true,
        ignoreInvalidCommands: false 
      });
      
      expect(() => {
        parserWithValidation.parsePath('X10,10');
      }).toThrow(SVGPathParseError);
    });

    it('should ignore invalid commands when configured', () => {
      const parserIgnoreInvalid = new SVGParser({ 
        ignoreInvalidCommands: true 
      });
      
      const commands = parserIgnoreInvalid.parsePath('M10,10 X20,20 L30,30');
      expect(commands).toHaveLength(2); // M and L commands, X ignored
    });
  });

  describe('SVG Document Parsing', () => {
    it('should parse simple SVG document', () => {
      const svgContent = `
        <svg width="100" height="100" viewBox="0 0 100 100">
          <path d="M10,10 L90,90" stroke="red" />
          <circle cx="50" cy="50" r="20" fill="blue" />
        </svg>
      `;
      
      const document = parser.parseSVG(svgContent);
      
      expect(document.width).toBe(100);
      expect(document.height).toBe(100);
      expect(document.viewBox).toEqual({
        x: 0, y: 0, width: 100, height: 100
      });
      expect(document.elements).toHaveLength(2);
    });

    it('should parse nested groups', () => {
      const svgContent = `
        <svg>
          <g id="group1">
            <path d="M0,0 L10,10" />
            <g id="group2">
              <circle cx="5" cy="5" r="2" />
            </g>
          </g>
        </svg>
      `;
      
      const document = parser.parseSVG(svgContent);
      
      expect(document.elements).toHaveLength(1);
      expect(document.elements[0].type).toBe('g');
      expect(document.elements[0].children).toHaveLength(2);
      expect(document.elements[0].children![1].children).toHaveLength(1);
    });

    it('should handle SVG with metadata', () => {
      const svgContent = `
        <svg>
          <title>Test SVG</title>
          <desc>A test SVG document</desc>
          <path d="M0,0 L10,10" />
        </svg>
      `;
      
      const document = parser.parseSVG(svgContent);
      
      expect(document.metadata?.title).toBe('Test SVG');
      expect(document.metadata?.description).toBe('A test SVG document');
    });

    it('should throw error for invalid SVG markup', () => {
      const invalidSvg = '<svg><path d="M10,10 L20,20"</svg>'; // Missing closing >
      
      expect(() => {
        parser.parseSVG(invalidSvg);
      }).toThrow(SVGParseError);
    });

    it('should throw error for non-SVG content', () => {
      const nonSvg = '<div>Not an SVG</div>';
      
      expect(() => {
        parser.parseSVG(nonSvg);
      }).toThrow(SVGParseError);
    });
  });

  describe('Validation', () => {
    it('should validate correct SVG document', () => {
      const svgContent = `
        <svg>
          <path d="M10,10 L20,20" />
          <circle cx="50" cy="50" r="10" />
        </svg>
      `;
      
      const document = parser.parseSVG(svgContent);
      const validation = parser.validate(document);
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.elementCount).toBe(2);
      expect(validation.pathCommandCount).toBe(2);
    });

    it('should report validation errors', () => {
      // This would require a document with actual validation issues
      // For now, we'll test the structure
      const document: ParsedSVGDocument = {
        elements: [],
        metadata: {}
      };
      
      const validation = parser.validate(document);
      
      expect(validation).toHaveProperty('isValid');
      expect(validation).toHaveProperty('errors');
      expect(validation).toHaveProperty('warnings');
      expect(validation).toHaveProperty('elementCount');
      expect(validation).toHaveProperty('pathCommandCount');
    });
  });

  describe('Parser Options', () => {
    it('should respect precision setting', () => {
      const highPrecisionParser = new SVGParser({ precision: 6 });
      const commands = highPrecisionParser.parsePath('M10.123456789,10.987654321');
      
      expect(commands[0].points[0]).toBe(10.123457); // Rounded to 6 decimal places
      expect(commands[0].points[1]).toBe(10.987654);
    });

    it('should handle different precision settings', () => {
      const lowPrecisionParser = new SVGParser({ precision: 1 });
      const commands = lowPrecisionParser.parsePath('M10.56,20.78');
      
      expect(commands[0].points[0]).toBe(10.6); // Rounded to 1 decimal place
      expect(commands[0].points[1]).toBe(20.8);
    });
  });

  describe('Error Handling', () => {
    it('should create proper error types', () => {
      try {
        parser.parsePath('Invalid path data X');
      } catch (error) {
        expect(error).toBeInstanceOf(SVGPathParseError);
        expect(error.name).toBe('SVGPathParseError');
        expect(error.message).toContain('Invalid');
      }
    });

    it('should include context in errors', () => {
      try {
        parser.parseSVG('<invalid>xml</invalid>');
      } catch (error) {
        expect(error).toBeInstanceOf(SVGParseError);
        expect(error.name).toBe('SVGParseError');
      }
    });
  });
});

// Mock implementations for testing environment
if (typeof global !== 'undefined') {
  // Node.js environment mocks
  global.DOMParser = class MockDOMParser {
    parseFromString(str: string, type: string) {
      // Simple mock implementation
      if (str.includes('parsererror') || str.includes('<invalid>')) {
        const doc = {
          querySelector: (selector: string) => {
            if (selector === 'parsererror') return {};
            if (selector === 'svg') return null;
            return null;
          }
        };
        return doc;
      }
      
      return {
        querySelector: (selector: string) => {
          if (selector === 'svg') {
            return {
              getAttribute: (attr: string) => {
                if (attr === 'width') return '100';
                if (attr === 'height') return '100';
                if (attr === 'viewBox') return '0 0 100 100';
                return null;
              },
              children: [],
              querySelector: (sel: string) => {
                if (sel === 'title') return { textContent: 'Test SVG' };
                if (sel === 'desc') return { textContent: 'A test SVG document' };
                return null;
              }
            };
          }
          return null;
        }
      };
    }
  };

  global.DOMMatrix = class MockDOMMatrix {
    constructor(transform?: string) {
      // Mock implementation
    }
  };
}
