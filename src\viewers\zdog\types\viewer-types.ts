/**
 * TypeScript type definitions for Zdog SVG Viewer configuration and state management
 * Comprehensive type safety for viewer options, controls, and events
 */

import type { Illustration, Anchor, Color } from 'zdog';
import type { ParsedSVGDocument, SVGParserOptions, CoordinateSystemOptions } from './svg-types';

// Core viewer configuration options
export interface ZdogViewerOptions {
  container: HTMLElement;
  width?: number;
  height?: number;
  backgroundColor?: Color;
  pixelRatio?: number;
  dragRotate?: boolean;
  autoResize?: boolean;
  zoom?: number;
  onReady?: (viewer: ZdogSVGViewer) => void;
  onError?: (error: Error) => void;
}

// SVG import and conversion options
export interface SVGImportOptions {
  depth?: number;
  strokeWidth?: number;
  preserveAspectRatio?: boolean;
  scale?: number;
  centerOrigin?: boolean;
  flipY?: boolean;
  parserOptions?: SVGParserOptions;
  coordinateOptions?: CoordinateSystemOptions;
  materialOptions?: MaterialOptions;
}

// Material and appearance options for 3D shapes
export interface MaterialOptions {
  defaultColor?: Color;
  defaultStroke?: number;
  enableBackface?: boolean;
  backfaceColor?: Color;
  wireframe?: boolean;
  opacity?: number;
  metallic?: boolean;
  roughness?: number;
}

// Interactive controls configuration
export interface ViewerControls {
  autoRotate: boolean;
  rotationSpeed: number;
  zoom: number;
  panX: number;
  panY: number;
  rotationX: number;
  rotationY: number;
  rotationZ: number;
  enableDrag: boolean;
  enableZoom: boolean;
  enablePan: boolean;
}

// Animation configuration
export interface AnimationOptions {
  enabled: boolean;
  duration: number;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-bezier';
  loop: boolean;
  autoStart: boolean;
  fps?: number;
}

// Performance monitoring interface
export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  shapeCount: number;
  renderTime: number;
  memoryUsage?: number;
  lastUpdateTime: number;
}

// Shape management interface
export interface ShapeInfo {
  id: string;
  type: string;
  zdogShape: Anchor;
  originalElement?: any;
  visible: boolean;
  depth: number;
  material: MaterialOptions;
  transform: {
    x: number;
    y: number;
    z: number;
    rotateX: number;
    rotateY: number;
    rotateZ: number;
    scale: number;
  };
}

// Event system types
export type ViewerEventType = 
  | 'load'
  | 'error'
  | 'render'
  | 'export'
  | 'shapeAdded'
  | 'shapeRemoved'
  | 'shapeUpdated'
  | 'animationStart'
  | 'animationEnd'
  | 'interactionStart'
  | 'interactionEnd'
  | 'resize';

export interface ViewerEvent<T = any> {
  type: ViewerEventType;
  data: T;
  timestamp: number;
  target: ZdogSVGViewer;
}

export interface LoadEvent extends ViewerEvent<{ shapeCount: number; parseTime: number }> {
  type: 'load';
}

export interface ErrorEvent extends ViewerEvent<{ error: Error; context?: string }> {
  type: 'error';
}

export interface RenderEvent extends ViewerEvent<PerformanceMetrics> {
  type: 'render';
}

export interface ExportEvent extends ViewerEvent<{ format: string; size: number; blob: Blob }> {
  type: 'export';
}

export interface ShapeEvent extends ViewerEvent<{ shape: ShapeInfo; action: 'add' | 'remove' | 'update' }> {
  type: 'shapeAdded' | 'shapeRemoved' | 'shapeUpdated';
}

export interface InteractionEvent extends ViewerEvent<{ type: 'drag' | 'zoom' | 'pan'; delta: { x: number; y: number } }> {
  type: 'interactionStart' | 'interactionEnd';
}

export interface ResizeEvent extends ViewerEvent<{ width: number; height: number }> {
  type: 'resize';
}

// Union type for all events
export type ViewerEventUnion = LoadEvent | ErrorEvent | RenderEvent | ExportEvent | ShapeEvent | InteractionEvent | ResizeEvent;

// Event handler types
export type EventHandler<T extends ViewerEvent = ViewerEvent> = (event: T) => void;
export type EventHandlerMap = Partial<Record<ViewerEventType, EventHandler[]>>;

// Export options
export interface ExportOptions {
  format: 'png' | 'svg' | 'webp' | 'jpeg';
  quality?: number; // For lossy formats
  width?: number;
  height?: number;
  backgroundColor?: Color;
  transparent?: boolean;
  scale?: number;
}

// Viewer state interface
export interface ViewerState {
  readonly isInitialized: boolean;
  readonly isLoading: boolean;
  readonly isAnimating: boolean;
  readonly shapes: ReadonlyMap<string, ShapeInfo>;
  readonly controls: Readonly<ViewerControls>;
  readonly performance: Readonly<PerformanceMetrics>;
  readonly lastRenderTime: number;
  readonly svgDocument?: ParsedSVGDocument;
}

// Plugin system interface
export interface ZdogPlugin {
  name: string;
  version: string;
  description?: string;
  dependencies?: string[];
  install(viewer: ZdogSVGViewer): void;
  uninstall?(viewer: ZdogSVGViewer): void;
}

// Plugin registry
export interface PluginRegistry {
  register(plugin: ZdogPlugin): void;
  unregister(pluginName: string): void;
  get(pluginName: string): ZdogPlugin | undefined;
  list(): ZdogPlugin[];
}

// Toolbar configuration
export interface ToolbarConfig {
  showDepthControl: boolean;
  showRotationControls: boolean;
  showZoomControls: boolean;
  showExportButton: boolean;
  showResetButton: boolean;
  showPerformanceMetrics: boolean;
  customButtons?: ToolbarButton[];
}

export interface ToolbarButton {
  id: string;
  label: string;
  icon?: string;
  tooltip?: string;
  onClick: (viewer: ZdogSVGViewer) => void;
  disabled?: boolean;
  visible?: boolean;
}

// Error handling
export class ZdogViewerError extends Error {
  constructor(
    message: string,
    public code?: string,
    public context?: any
  ) {
    super(message);
    this.name = 'ZdogViewerError';
  }
}

export class ZdogRenderError extends ZdogViewerError {
  constructor(
    message: string,
    public shapeId?: string,
    context?: any
  ) {
    super(message, 'RENDER_ERROR', context);
    this.name = 'ZdogRenderError';
  }
}

export class ZdogExportError extends ZdogViewerError {
  constructor(
    message: string,
    public format?: string,
    context?: any
  ) {
    super(message, 'EXPORT_ERROR', context);
    this.name = 'ZdogExportError';
  }
}

// Forward declaration for the main viewer class
export interface ZdogSVGViewer {
  // Core methods
  loadSVG(svgContent: string, options?: SVGImportOptions): Promise<void>;
  updateShape(id: string, properties: Partial<ShapeInfo>): void;
  removeShape(id: string): boolean;
  exportImage(options?: ExportOptions): Promise<Blob>;
  destroy(): void;
  
  // State access
  getState(): ViewerState;
  getShape(id: string): ShapeInfo | undefined;
  getAllShapes(): ShapeInfo[];
  
  // Controls
  setControls(controls: Partial<ViewerControls>): void;
  resetView(): void;
  startAnimation(): void;
  stopAnimation(): void;
  
  // Events
  on<T extends ViewerEvent>(eventType: T['type'], handler: EventHandler<T>): void;
  off<T extends ViewerEvent>(eventType: T['type'], handler: EventHandler<T>): void;
  emit<T extends ViewerEvent>(event: T): void;
  
  // Plugin system
  installPlugin(plugin: ZdogPlugin): void;
  uninstallPlugin(pluginName: string): void;
}
