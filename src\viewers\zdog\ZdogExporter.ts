/**
 * Zdog Exporter - Handle export functionality for the 3D SVG viewer
 * Supports PNG, SVG, WebP, and JPEG export with TypeScript type safety
 */

import type { Illustration } from 'zdog';
import type {
  ExportOptions,
  ZdogSVGViewer
} from './types/viewer-types';

// Import the error class for runtime use
import { ZdogExportError } from './types/viewer-types';

export class ZdogExporter {
  private viewer: Z<PERSON><PERSON><PERSON>Viewer;
  private illustration: Illustration;

  constructor(viewer: <PERSON><PERSON><PERSON><PERSON>Viewer, illustration: Illustration) {
    this.viewer = viewer;
    this.illustration = illustration;
  }

  /**
   * Export the current view as an image
   */
  public async exportImage(options: ExportOptions = { format: 'png' }): Promise<Blob> {
    const defaultOptions: Required<ExportOptions> = {
      format: options.format || 'png',
      quality: options.quality ?? 0.9,
      width: options.width ?? this.illustration.width,
      height: options.height ?? this.illustration.height,
      backgroundColor: options.backgroundColor ?? '#ffffff',
      transparent: options.transparent ?? false,
      scale: options.scale ?? 1
    };

    try {
      switch (defaultOptions.format) {
        case 'png':
          return await this.exportPNG(defaultOptions);
        case 'svg':
          return await this.exportSVG(defaultOptions);
        case 'webp':
          return await this.exportWebP(defaultOptions);
        case 'jpeg':
          return await this.exportJPEG(defaultOptions);
        default:
          throw new ZdogExportError(`Unsupported export format: ${defaultOptions.format}`, defaultOptions.format);
      }
    } catch (error) {
      if (error instanceof ZdogExportError) {
        throw error;
      }
      throw new ZdogExportError(
        `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        defaultOptions.format
      );
    }
  }

  /**
   * Export as PNG
   */
  private async exportPNG(options: Required<ExportOptions>): Promise<Blob> {
    const canvas = await this.createCanvas(options);
    
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new ZdogExportError('Failed to create PNG blob', 'png'));
          }
        },
        'image/png'
      );
    });
  }

  /**
   * Export as WebP
   */
  private async exportWebP(options: Required<ExportOptions>): Promise<Blob> {
    const canvas = await this.createCanvas(options);
    
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new ZdogExportError('Failed to create WebP blob', 'webp'));
          }
        },
        'image/webp',
        options.quality
      );
    });
  }

  /**
   * Export as JPEG
   */
  private async exportJPEG(options: Required<ExportOptions>): Promise<Blob> {
    const canvas = await this.createCanvas(options);
    
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new ZdogExportError('Failed to create JPEG blob', 'jpeg'));
          }
        },
        'image/jpeg',
        options.quality
      );
    });
  }

  /**
   * Export as SVG
   */
  private async exportSVG(options: Required<ExportOptions>): Promise<Blob> {
    try {
      // Create SVG element
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('width', options.width.toString());
      svg.setAttribute('height', options.height.toString());
      svg.setAttribute('viewBox', `0 0 ${options.width} ${options.height}`);
      
      if (!options.transparent && options.backgroundColor) {
        const background = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        background.setAttribute('width', '100%');
        background.setAttribute('height', '100%');
        background.setAttribute('fill', this.colorToString(options.backgroundColor));
        svg.appendChild(background);
      }

      // Render Zdog illustration to SVG
      this.illustration.renderGraphSvg(svg);

      // Convert to blob
      const serializer = new XMLSerializer();
      const svgString = serializer.serializeToString(svg);
      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      
      return blob;
    } catch (error) {
      throw new ZdogExportError(
        `SVG export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'svg'
      );
    }
  }

  /**
   * Create a canvas for raster image export
   */
  private async createCanvas(options: Required<ExportOptions>): Promise<HTMLCanvasElement> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new ZdogExportError('Failed to get canvas 2D context');
    }

    // Set canvas size
    canvas.width = options.width * options.scale;
    canvas.height = options.height * options.scale;
    
    // Scale context for high DPI
    if (options.scale !== 1) {
      ctx.scale(options.scale, options.scale);
    }

    // Set background
    if (!options.transparent) {
      ctx.fillStyle = this.colorToString(options.backgroundColor);
      ctx.fillRect(0, 0, options.width, options.height);
    }

    // Create temporary illustration for export
    const tempIllustration = this.createTempIllustration(canvas, options);
    
    // Copy shapes from original illustration
    this.copyShapesToTempIllustration(tempIllustration);
    
    // Render to canvas
    tempIllustration.renderGraphCanvas(ctx);
    
    return canvas;
  }

  /**
   * Create temporary illustration for export
   */
  private createTempIllustration(canvas: HTMLCanvasElement, options: Required<ExportOptions>): Illustration {
    // Import Illustration class dynamically to avoid circular dependencies
    const { Illustration } = require('zdog');
    
    return new Illustration({
      element: canvas,
      width: options.width,
      height: options.height,
      zoom: this.illustration.zoom,
      dragRotate: false,
      resize: false
    });
  }

  /**
   * Copy shapes from original illustration to temporary one
   */
  private copyShapesToTempIllustration(tempIllustration: Illustration): void {
    // Get all shapes from the viewer
    const shapes = this.viewer.getAllShapes();
    
    shapes.forEach(shapeInfo => {
      if (shapeInfo.visible) {
        // Copy the shape to the temporary illustration
        const copiedShape = shapeInfo.zdogShape.copyGraph({
          addTo: tempIllustration
        });
        
        // Apply any transformations
        if (shapeInfo.transform) {
          copiedShape.translate.x = shapeInfo.transform.x;
          copiedShape.translate.y = shapeInfo.transform.y;
          copiedShape.translate.z = shapeInfo.transform.z;

          copiedShape.rotate.x = shapeInfo.transform.rotateX;
          copiedShape.rotate.y = shapeInfo.transform.rotateY;
          copiedShape.rotate.z = shapeInfo.transform.rotateZ;

          if (typeof copiedShape.scale === 'object') {
            (copiedShape.scale as any).x = shapeInfo.transform.scale;
            (copiedShape.scale as any).y = shapeInfo.transform.scale;
            (copiedShape.scale as any).z = shapeInfo.transform.scale;
          } else {
            (copiedShape as any).scale = shapeInfo.transform.scale;
          }
        }
      }
    });

    // Copy illustration transforms
    tempIllustration.rotate.x = this.illustration.rotate.x;
    tempIllustration.rotate.y = this.illustration.rotate.y;
    tempIllustration.rotate.z = this.illustration.rotate.z;

    tempIllustration.translate.x = this.illustration.translate.x;
    tempIllustration.translate.y = this.illustration.translate.y;
    tempIllustration.translate.z = this.illustration.translate.z;
  }

  /**
   * Convert Color type to string
   */
  private colorToString(color: any): string {
    if (typeof color === 'string') {
      return color;
    }

    if (color && typeof color === 'object' && 'r' in color && 'g' in color && 'b' in color) {
      const r = Math.round(color.r * 255);
      const g = Math.round(color.g * 255);
      const b = Math.round(color.b * 255);
      const a = color.a !== undefined ? color.a : 1;

      if (a < 1) {
        return `rgba(${r}, ${g}, ${b}, ${a})`;
      } else {
        return `rgb(${r}, ${g}, ${b})`;
      }
    }

    return '#000000'; // Default fallback
  }

  /**
   * Get export capabilities
   */
  public static getExportCapabilities(): {
    formats: string[];
    maxWidth: number;
    maxHeight: number;
    supportsTransparency: string[];
    supportsQuality: string[];
  } {
    return {
      formats: ['png', 'svg', 'webp', 'jpeg'],
      maxWidth: 8192,
      maxHeight: 8192,
      supportsTransparency: ['png', 'svg', 'webp'],
      supportsQuality: ['webp', 'jpeg']
    };
  }

  /**
   * Validate export options
   */
  public static validateExportOptions(options: ExportOptions): string[] {
    const errors: string[] = [];
    const capabilities = ZdogExporter.getExportCapabilities();

    if (options.format && !capabilities.formats.includes(options.format)) {
      errors.push(`Unsupported format: ${options.format}`);
    }

    if (options.width && (options.width <= 0 || options.width > capabilities.maxWidth)) {
      errors.push(`Width must be between 1 and ${capabilities.maxWidth}`);
    }

    if (options.height && (options.height <= 0 || options.height > capabilities.maxHeight)) {
      errors.push(`Height must be between 1 and ${capabilities.maxHeight}`);
    }

    if (options.quality && (options.quality < 0 || options.quality > 1)) {
      errors.push('Quality must be between 0 and 1');
    }

    if (options.scale && (options.scale <= 0 || options.scale > 4)) {
      errors.push('Scale must be between 0 and 4');
    }

    if (options.transparent && options.format && !capabilities.supportsTransparency.includes(options.format)) {
      errors.push(`Format ${options.format} does not support transparency`);
    }

    return errors;
  }

  /**
   * Create download link for blob
   */
  public static downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    // Clean up the URL object
    setTimeout(() => URL.revokeObjectURL(url), 100);
  }

  /**
   * Get suggested filename for export
   */
  public static getSuggestedFilename(format: string, prefix: string = 'zdog-export'): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    return `${prefix}-${timestamp}.${format}`;
  }
}
