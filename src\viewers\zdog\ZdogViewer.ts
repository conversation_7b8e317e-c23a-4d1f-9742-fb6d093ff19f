/**
 * Main Zdog SVG Viewer class with comprehensive TypeScript support
 * Handles SVG loading, 3D conversion, animation, and user interaction
 */

import { Illustration, Anchor, Shape, Group, Vector3d, Color } from 'zdog';
import { SVGParser } from './SVGParser';
import { ZdogExporter } from './ZdogExporter';
import type {
  ZdogViewerOptions,
  SVGImportOptions,
  ViewerControls,
  ViewerState,
  ShapeInfo,
  PerformanceMetrics,
  ViewerEventUnion,
  EventHandler,
  EventHandlerMap,
  ExportOptions,
  ZdogPlugin,
  AnimationOptions
} from './types/viewer-types';

import {
  ZdogViewerError,
  ZdogRenderError,
  ZdogExportError
} from './types/viewer-types';
import type {
  ParsedSVGDocument,
  ParsedSVGElement,
  SVGPathCommandUnion
} from './types/svg-types';

export class ZdogSVGViewer {
  private illustration: Illustration;
  private shapes: Map<string, ShapeInfo> = new Map();
  private controls: ViewerControls;
  private eventHandlers: EventHandlerMap = {};
  private animationFrameId: number | null = null;
  private isInitialized = false;
  private isLoading = false;
  private isAnimating = false;
  private svgDocument?: ParsedSVGDocument;
  private parser: SVGParser;
  private exporter: ZdogExporter;
  private plugins: Map<string, ZdogPlugin> = new Map();
  
  // Performance tracking
  private performance: PerformanceMetrics = {
    fps: 0,
    frameTime: 0,
    shapeCount: 0,
    renderTime: 0,
    lastUpdateTime: 0
  };

  private lastFrameTime = 0;
  private frameCount = 0;
  private fpsUpdateInterval = 1000; // Update FPS every second

  constructor(private options: ZdogViewerOptions) {
    this.validateOptions(options);
    this.initializeControls();
    this.parser = new SVGParser();
    this.setupIllustration();
    this.exporter = new ZdogExporter(this, this.illustration);
    this.setupEventListeners();
    this.isInitialized = true;

    // Emit ready event
    if (this.options.onReady) {
      this.options.onReady(this);
    }
  }

  /**
   * Load SVG content and convert to 3D shapes
   */
  public async loadSVG(svgContent: string, options: SVGImportOptions = {}): Promise<void> {
    if (this.isLoading) {
      throw new ZdogViewerError('Already loading SVG content');
    }

    this.isLoading = true;
    const startTime = performance.now();

    try {
      // Clear existing shapes
      this.clearShapes();

      // Parse SVG
      this.svgDocument = this.parser.parseSVG(svgContent);
      
      // Validate document
      const validation = this.parser.validate(this.svgDocument);
      if (!validation.isValid) {
        throw new ZdogViewerError(`Invalid SVG: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      // Convert to Zdog shapes
      await this.convertSVGToZdog(this.svgDocument, options);

      // Update performance metrics
      const parseTime = performance.now() - startTime;
      this.performance.shapeCount = this.shapes.size;

      // Emit load event
      this.emit({
        type: 'load',
        data: { shapeCount: this.shapes.size, parseTime },
        timestamp: Date.now(),
        target: this
      });

      // Start rendering
      this.render();

    } catch (error) {
      const viewerError = error instanceof ZdogViewerError ? error : 
        new ZdogViewerError(`Failed to load SVG: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      this.emit({
        type: 'error',
        data: { error: viewerError },
        timestamp: Date.now(),
        target: this
      });

      throw viewerError;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Convert parsed SVG document to Zdog shapes
   */
  private async convertSVGToZdog(document: ParsedSVGDocument, options: SVGImportOptions): Promise<void> {
    const defaultOptions: Required<SVGImportOptions> = {
      depth: options.depth ?? 10,
      strokeWidth: options.strokeWidth ?? 2,
      preserveAspectRatio: options.preserveAspectRatio ?? true,
      scale: options.scale ?? 1,
      centerOrigin: options.centerOrigin ?? true,
      flipY: options.flipY ?? true,
      parserOptions: options.parserOptions ?? {},
      coordinateOptions: options.coordinateOptions ?? {},
      materialOptions: options.materialOptions ?? {}
    };

    // Calculate coordinate system adjustments
    const { offsetX, offsetY, scaleX, scaleY } = this.calculateCoordinateTransform(document, defaultOptions);

    // Convert each element
    for (const element of document.elements) {
      await this.convertElement(element, defaultOptions, { offsetX, offsetY, scaleX, scaleY });
    }
  }

  /**
   * Convert individual SVG element to Zdog shape
   */
  private async convertElement(
    element: ParsedSVGElement, 
    options: Required<SVGImportOptions>,
    transform: { offsetX: number; offsetY: number; scaleX: number; scaleY: number }
  ): Promise<void> {
    try {
      let zdogShape: Anchor | null = null;

      switch (element.type) {
        case 'path':
          zdogShape = this.createPathShape(element, options, transform);
          break;
        case 'circle':
          zdogShape = this.createCircleShape(element, options, transform);
          break;
        case 'rect':
          zdogShape = this.createRectShape(element, options, transform);
          break;
        case 'line':
          zdogShape = this.createLineShape(element, options, transform);
          break;
        case 'ellipse':
          zdogShape = this.createEllipseShape(element, options, transform);
          break;
        case 'polygon':
          zdogShape = this.createPolygonShape(element, options, transform);
          break;
        case 'polyline':
          zdogShape = this.createPolylineShape(element, options, transform);
          break;
        case 'g':
          zdogShape = this.createGroupShape(element, options, transform);
          break;
        default:
          console.warn(`Unsupported element type: ${element.type}`);
          return;
      }

      if (zdogShape) {
        const shapeInfo: ShapeInfo = {
          id: element.id || `shape_${this.shapes.size}`,
          type: element.type,
          zdogShape,
          originalElement: element,
          visible: true,
          depth: options.depth,
          material: options.materialOptions,
          transform: {
            x: 0, y: 0, z: 0,
            rotateX: 0, rotateY: 0, rotateZ: 0,
            scale: 1
          }
        };

        this.shapes.set(shapeInfo.id, shapeInfo);

        this.emit({
          type: 'shapeAdded',
          data: { shape: shapeInfo, action: 'add' },
          timestamp: Date.now(),
          target: this
        });
      }

      // Handle child elements for groups
      if (element.children) {
        for (const child of element.children) {
          await this.convertElement(child, options, transform);
        }
      }

    } catch (error) {
      throw new ZdogRenderError(
        `Failed to convert element ${element.type}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element.id
      );
    }
  }

  /**
   * Create Zdog shape from SVG path element
   */
  private createPathShape(
    element: ParsedSVGElement, 
    options: Required<SVGImportOptions>,
    transform: { offsetX: number; offsetY: number; scaleX: number; scaleY: number }
  ): Shape | null {
    if (!element.pathCommands || element.pathCommands.length === 0) {
      return null;
    }

    const pathPoints = this.convertPathCommandsToPoints(element.pathCommands, transform);
    if (pathPoints.length === 0) {
      return null;
    }

    const color = this.extractColor(element) || options.materialOptions.defaultColor || '#333';
    const stroke = this.extractStrokeWidth(element) || options.strokeWidth;

    return new Shape({
      addTo: this.illustration,
      path: pathPoints,
      stroke,
      color,
      fill: this.shouldFill(element),
      translate: { z: options.depth / 2 }
    });
  }

  /**
   * Convert SVG path commands to Zdog path points
   */
  private convertPathCommandsToPoints(
    commands: SVGPathCommandUnion[], 
    transform: { offsetX: number; offsetY: number; scaleX: number; scaleY: number }
  ): any[] {
    const points: any[] = [];
    let currentX = 0;
    let currentY = 0;

    for (const command of commands) {
      switch (command.type) {
        case 'M':
          currentX = (command.points[0] + transform.offsetX) * transform.scaleX;
          currentY = (command.points[1] + transform.offsetY) * transform.scaleY;
          points.push({ move: { x: currentX, y: currentY, z: 0 } });
          break;

        case 'L':
          currentX = (command.points[0] + transform.offsetX) * transform.scaleX;
          currentY = (command.points[1] + transform.offsetY) * transform.scaleY;
          points.push({ x: currentX, y: currentY, z: 0 });
          break;

        case 'C':
          // Cubic bezier curve - approximate with line segments for now
          const cubicCmd = command as any;
          const endX = (cubicCmd.x + transform.offsetX) * transform.scaleX;
          const endY = (cubicCmd.y + transform.offsetY) * transform.scaleY;
          points.push({ x: endX, y: endY, z: 0 });
          currentX = endX;
          currentY = endY;
          break;

        case 'Z':
          // Close path
          break;

        default:
          console.warn(`Unsupported path command: ${command.type}`);
      }
    }

    return points;
  }

  /**
   * Create circle shape from SVG circle element
   */
  private createCircleShape(element: ParsedSVGElement, options: Required<SVGImportOptions>, transform: any): Shape | null {
    const cx = parseFloat(element.attributes.cx || '0');
    const cy = parseFloat(element.attributes.cy || '0');
    const r = parseFloat(element.attributes.r || '0');

    if (r <= 0) return null;

    const color = this.extractColor(element) || options.materialOptions.defaultColor || '#333';
    const stroke = this.extractStrokeWidth(element) || options.strokeWidth;

    // Create circle as a path
    const pathPoints = [];
    const segments = 32; // Number of segments for circle approximation

    for (let i = 0; i <= segments; i++) {
      const angle = (i / segments) * Math.PI * 2;
      const x = (cx + r * Math.cos(angle) + transform.offsetX) * transform.scaleX;
      const y = (cy + r * Math.sin(angle) + transform.offsetY) * transform.scaleY;

      if (i === 0) {
        pathPoints.push({ move: { x, y, z: 0 } });
      } else {
        pathPoints.push({ x, y, z: 0 });
      }
    }

    return new Shape({
      addTo: this.illustration,
      path: pathPoints,
      stroke,
      color,
      fill: this.shouldFill(element),
      closed: true,
      translate: { z: options.depth / 2 }
    });
  }

  /**
   * Create rectangle shape from SVG rect element
   */
  private createRectShape(element: ParsedSVGElement, options: Required<SVGImportOptions>, transform: any): Shape | null {
    const x = parseFloat(element.attributes.x || '0');
    const y = parseFloat(element.attributes.y || '0');
    const width = parseFloat(element.attributes.width || '0');
    const height = parseFloat(element.attributes.height || '0');

    if (width <= 0 || height <= 0) return null;

    const color = this.extractColor(element) || options.materialOptions.defaultColor || '#333';
    const stroke = this.extractStrokeWidth(element) || options.strokeWidth;

    // Create rectangle as a path
    const x1 = (x + transform.offsetX) * transform.scaleX;
    const y1 = (y + transform.offsetY) * transform.scaleY;
    const x2 = (x + width + transform.offsetX) * transform.scaleX;
    const y2 = (y + height + transform.offsetY) * transform.scaleY;

    const pathPoints = [
      { move: { x: x1, y: y1, z: 0 } },
      { x: x2, y: y1, z: 0 },
      { x: x2, y: y2, z: 0 },
      { x: x1, y: y2, z: 0 }
    ];

    return new Shape({
      addTo: this.illustration,
      path: pathPoints,
      stroke,
      color,
      fill: this.shouldFill(element),
      closed: true,
      translate: { z: options.depth / 2 }
    });
  }

  /**
   * Create line shape from SVG line element
   */
  private createLineShape(element: ParsedSVGElement, options: Required<SVGImportOptions>, transform: any): Shape | null {
    const x1 = parseFloat(element.attributes.x1 || '0');
    const y1 = parseFloat(element.attributes.y1 || '0');
    const x2 = parseFloat(element.attributes.x2 || '0');
    const y2 = parseFloat(element.attributes.y2 || '0');

    const color = this.extractColor(element) || options.materialOptions.defaultColor || '#333';
    const stroke = this.extractStrokeWidth(element) || options.strokeWidth;

    const startX = (x1 + transform.offsetX) * transform.scaleX;
    const startY = (y1 + transform.offsetY) * transform.scaleY;
    const endX = (x2 + transform.offsetX) * transform.scaleX;
    const endY = (y2 + transform.offsetY) * transform.scaleY;

    const pathPoints = [
      { move: { x: startX, y: startY, z: 0 } },
      { x: endX, y: endY, z: 0 }
    ];

    return new Shape({
      addTo: this.illustration,
      path: pathPoints,
      stroke,
      color,
      fill: false,
      translate: { z: options.depth / 2 }
    });
  }

  /**
   * Create ellipse shape from SVG ellipse element
   */
  private createEllipseShape(element: ParsedSVGElement, options: Required<SVGImportOptions>, transform: any): Shape | null {
    const cx = parseFloat(element.attributes.cx || '0');
    const cy = parseFloat(element.attributes.cy || '0');
    const rx = parseFloat(element.attributes.rx || '0');
    const ry = parseFloat(element.attributes.ry || '0');

    if (rx <= 0 || ry <= 0) return null;

    const color = this.extractColor(element) || options.materialOptions.defaultColor || '#333';
    const stroke = this.extractStrokeWidth(element) || options.strokeWidth;

    // Create ellipse as a path
    const pathPoints = [];
    const segments = 32;

    for (let i = 0; i <= segments; i++) {
      const angle = (i / segments) * Math.PI * 2;
      const x = (cx + rx * Math.cos(angle) + transform.offsetX) * transform.scaleX;
      const y = (cy + ry * Math.sin(angle) + transform.offsetY) * transform.scaleY;

      if (i === 0) {
        pathPoints.push({ move: { x, y, z: 0 } });
      } else {
        pathPoints.push({ x, y, z: 0 });
      }
    }

    return new Shape({
      addTo: this.illustration,
      path: pathPoints,
      stroke,
      color,
      fill: this.shouldFill(element),
      closed: true,
      translate: { z: options.depth / 2 }
    });
  }

  /**
   * Create polygon shape from SVG polygon element
   */
  private createPolygonShape(element: ParsedSVGElement, options: Required<SVGImportOptions>, transform: any): Shape | null {
    const pointsAttr = element.attributes.points;
    if (!pointsAttr) return null;

    const color = this.extractColor(element) || options.materialOptions.defaultColor || '#333';
    const stroke = this.extractStrokeWidth(element) || options.strokeWidth;

    // Parse points
    const points = pointsAttr.trim().split(/[\s,]+/).map(parseFloat);
    if (points.length < 4 || points.length % 2 !== 0) return null;

    const pathPoints = [];
    for (let i = 0; i < points.length; i += 2) {
      const x = (points[i] + transform.offsetX) * transform.scaleX;
      const y = (points[i + 1] + transform.offsetY) * transform.scaleY;

      if (i === 0) {
        pathPoints.push({ move: { x, y, z: 0 } });
      } else {
        pathPoints.push({ x, y, z: 0 });
      }
    }

    return new Shape({
      addTo: this.illustration,
      path: pathPoints,
      stroke,
      color,
      fill: this.shouldFill(element),
      closed: true,
      translate: { z: options.depth / 2 }
    });
  }

  /**
   * Create polyline shape from SVG polyline element
   */
  private createPolylineShape(element: ParsedSVGElement, options: Required<SVGImportOptions>, transform: any): Shape | null {
    const pointsAttr = element.attributes.points;
    if (!pointsAttr) return null;

    const color = this.extractColor(element) || options.materialOptions.defaultColor || '#333';
    const stroke = this.extractStrokeWidth(element) || options.strokeWidth;

    // Parse points
    const points = pointsAttr.trim().split(/[\s,]+/).map(parseFloat);
    if (points.length < 4 || points.length % 2 !== 0) return null;

    const pathPoints = [];
    for (let i = 0; i < points.length; i += 2) {
      const x = (points[i] + transform.offsetX) * transform.scaleX;
      const y = (points[i + 1] + transform.offsetY) * transform.scaleY;

      if (i === 0) {
        pathPoints.push({ move: { x, y, z: 0 } });
      } else {
        pathPoints.push({ x, y, z: 0 });
      }
    }

    return new Shape({
      addTo: this.illustration,
      path: pathPoints,
      stroke,
      color,
      fill: this.shouldFill(element),
      closed: false,
      translate: { z: options.depth / 2 }
    });
  }

  /**
   * Create group shape from SVG group element
   */
  private createGroupShape(element: ParsedSVGElement, options: Required<SVGImportOptions>, transform: any): Group | null {
    return new Group({
      addTo: this.illustration
    });
  }

  /**
   * Calculate coordinate system transformation
   */
  private calculateCoordinateTransform(document: ParsedSVGDocument, options: Required<SVGImportOptions>) {
    let offsetX = 0;
    let offsetY = 0;
    let scaleX = options.scale;
    let scaleY = options.scale;

    if (options.flipY) {
      scaleY = -scaleY;
    }

    if (options.centerOrigin && document.viewBox) {
      offsetX = -document.viewBox.width / 2;
      offsetY = -document.viewBox.height / 2;
    }

    return { offsetX, offsetY, scaleX, scaleY };
  }

  /**
   * Extract color from SVG element
   */
  private extractColor(element: ParsedSVGElement): string | null {
    return element.attributes.fill || element.attributes.stroke || null;
  }

  /**
   * Extract stroke width from SVG element
   */
  private extractStrokeWidth(element: ParsedSVGElement): number | null {
    const strokeWidth = element.attributes['stroke-width'];
    return strokeWidth ? parseFloat(strokeWidth) : null;
  }

  /**
   * Determine if element should be filled
   */
  private shouldFill(element: ParsedSVGElement): boolean {
    const fill = element.attributes.fill;
    return fill !== 'none' && fill !== undefined;
  }

  /**
   * Initialize default controls
   */
  private initializeControls(): void {
    this.controls = {
      autoRotate: false,
      rotationSpeed: 0.01,
      zoom: 1,
      panX: 0,
      panY: 0,
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0,
      enableDrag: true,
      enableZoom: true,
      enablePan: true
    };
  }

  /**
   * Setup Zdog illustration
   */
  private setupIllustration(): void {
    this.illustration = new Illustration({
      element: this.options.container,
      width: this.options.width || this.options.container.clientWidth,
      height: this.options.height || this.options.container.clientHeight,
      zoom: this.options.zoom || 1,
      dragRotate: this.options.dragRotate ?? true,
      resize: this.options.autoResize ?? true
    });
  }

  /**
   * Setup event listeners for interaction and resize
   */
  private setupEventListeners(): void {
    if (this.options.autoResize) {
      const resizeObserver = new ResizeObserver(() => {
        this.handleResize();
      });
      resizeObserver.observe(this.options.container);
    }

    // Setup interaction event listeners
    this.setupInteractionListeners();
  }

  /**
   * Setup interaction event listeners
   */
  private setupInteractionListeners(): void {
    const container = this.options.container;

    // Mouse/touch interaction state
    let isDragging = false;
    let lastPointerX = 0;
    let lastPointerY = 0;
    let startRotationX = 0;
    let startRotationY = 0;

    // Mouse events
    const handlePointerDown = (event: MouseEvent | TouchEvent) => {
      if (!this.controls.enableDrag) return;

      isDragging = true;
      const pointer = this.getPointerPosition(event);
      lastPointerX = pointer.x;
      lastPointerY = pointer.y;
      startRotationX = this.illustration.rotate.x;
      startRotationY = this.illustration.rotate.y;

      container.style.cursor = 'grabbing';

      this.emit({
        type: 'interactionStart',
        data: { type: 'drag', delta: { x: 0, y: 0 } },
        timestamp: Date.now(),
        target: this
      });

      event.preventDefault();
    };

    const handlePointerMove = (event: MouseEvent | TouchEvent) => {
      if (!isDragging || !this.controls.enableDrag) return;

      const pointer = this.getPointerPosition(event);
      const deltaX = pointer.x - lastPointerX;
      const deltaY = pointer.y - lastPointerY;

      // Apply rotation based on drag
      const sensitivity = 0.01;
      this.illustration.rotate.y = startRotationY + deltaX * sensitivity;
      this.illustration.rotate.x = startRotationX - deltaY * sensitivity;

      // Update controls to reflect current rotation
      this.controls.rotationX = this.illustration.rotate.x;
      this.controls.rotationY = this.illustration.rotate.y;

      // Render immediately for smooth interaction
      if (!this.isAnimating) {
        this.illustration.updateRenderGraph();
      }

      event.preventDefault();
    };

    const handlePointerUp = () => {
      if (!isDragging) return;

      isDragging = false;
      container.style.cursor = this.controls.enableDrag ? 'grab' : 'default';

      this.emit({
        type: 'interactionEnd',
        data: { type: 'drag', delta: { x: 0, y: 0 } },
        timestamp: Date.now(),
        target: this
      });
    };

    // Wheel event for zooming
    const handleWheel = (event: WheelEvent) => {
      if (!this.controls.enableZoom) return;

      event.preventDefault();

      const zoomSensitivity = 0.001;
      const zoomDelta = -event.deltaY * zoomSensitivity;
      const newZoom = Math.max(0.1, Math.min(5, this.controls.zoom + zoomDelta));

      this.controls.zoom = newZoom;
      this.illustration.zoom = newZoom;

      // Render immediately for smooth zooming
      if (!this.isAnimating) {
        this.illustration.updateRenderGraph();
      }
    };

    // Keyboard events for additional controls
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!container.contains(document.activeElement)) return;

      const step = 0.1;
      let handled = false;

      switch (event.code) {
        case 'ArrowLeft':
          this.controls.rotationY -= step;
          handled = true;
          break;
        case 'ArrowRight':
          this.controls.rotationY += step;
          handled = true;
          break;
        case 'ArrowUp':
          this.controls.rotationX -= step;
          handled = true;
          break;
        case 'ArrowDown':
          this.controls.rotationX += step;
          handled = true;
          break;
        case 'Space':
          this.controls.autoRotate = !this.controls.autoRotate;
          if (this.controls.autoRotate) {
            this.startAnimation();
          } else {
            this.stopAnimation();
          }
          handled = true;
          break;
        case 'KeyR':
          this.resetView();
          handled = true;
          break;
      }

      if (handled) {
        event.preventDefault();
        if (!this.isAnimating) {
          this.illustration.updateRenderGraph();
        }
      }
    };

    // Add event listeners
    container.addEventListener('mousedown', handlePointerDown);
    container.addEventListener('touchstart', handlePointerDown, { passive: false });

    document.addEventListener('mousemove', handlePointerMove);
    document.addEventListener('touchmove', handlePointerMove, { passive: false });

    document.addEventListener('mouseup', handlePointerUp);
    document.addEventListener('touchend', handlePointerUp);

    container.addEventListener('wheel', handleWheel, { passive: false });
    container.addEventListener('keydown', handleKeyDown);

    // Make container focusable for keyboard events
    if (!container.hasAttribute('tabindex')) {
      container.setAttribute('tabindex', '0');
    }

    // Set initial cursor
    container.style.cursor = this.controls.enableDrag ? 'grab' : 'default';

    // Store cleanup function
    this.cleanupInteractionListeners = () => {
      container.removeEventListener('mousedown', handlePointerDown);
      container.removeEventListener('touchstart', handlePointerDown);
      document.removeEventListener('mousemove', handlePointerMove);
      document.removeEventListener('touchmove', handlePointerMove);
      document.removeEventListener('mouseup', handlePointerUp);
      document.removeEventListener('touchend', handlePointerUp);
      container.removeEventListener('wheel', handleWheel);
      container.removeEventListener('keydown', handleKeyDown);
    };
  }

  private cleanupInteractionListeners?: () => void;

  /**
   * Get pointer position from mouse or touch event
   */
  private getPointerPosition(event: MouseEvent | TouchEvent): { x: number; y: number } {
    if (event instanceof MouseEvent) {
      return { x: event.clientX, y: event.clientY };
    } else {
      const touch = event.touches[0] || event.changedTouches[0];
      return { x: touch.clientX, y: touch.clientY };
    }
  }

  /**
   * Handle container resize
   */
  private handleResize(): void {
    const width = this.options.container.clientWidth;
    const height = this.options.container.clientHeight;
    
    this.illustration.setSize(width, height);
    
    this.emit({
      type: 'resize',
      data: { width, height },
      timestamp: Date.now(),
      target: this
    });
  }

  /**
   * Validate constructor options
   */
  private validateOptions(options: ZdogViewerOptions): void {
    if (!options.container) {
      throw new ZdogViewerError('Container element is required');
    }
    
    if (!(options.container instanceof HTMLElement)) {
      throw new ZdogViewerError('Container must be an HTMLElement');
    }
  }

  /**
   * Clear all shapes
   */
  private clearShapes(): void {
    for (const [id, shapeInfo] of this.shapes) {
      shapeInfo.zdogShape.remove();
      this.emit({
        type: 'shapeRemoved',
        data: { shape: shapeInfo, action: 'remove' },
        timestamp: Date.now(),
        target: this
      });
    }
    this.shapes.clear();
  }

  /**
   * Main render loop with enhanced animation and interaction
   */
  public render(): void {
    const now = performance.now();
    this.performance.frameTime = now - this.lastFrameTime;
    this.lastFrameTime = now;

    // Update FPS
    this.frameCount++;
    if (now - this.performance.lastUpdateTime >= this.fpsUpdateInterval) {
      this.performance.fps = Math.round((this.frameCount * 1000) / (now - this.performance.lastUpdateTime));
      this.frameCount = 0;
      this.performance.lastUpdateTime = now;
    }

    // Apply smooth auto-rotation with easing
    if (this.controls.autoRotate) {
      const rotationDelta = this.controls.rotationSpeed * (this.performance.frameTime / 16.67); // Normalize to 60fps
      this.illustration.rotate.y += rotationDelta;

      // Apply secondary rotation for more dynamic movement
      this.illustration.rotate.x += rotationDelta * 0.3;
    }

    // Apply manual rotation controls
    if (this.controls.rotationX !== 0 || this.controls.rotationY !== 0 || this.controls.rotationZ !== 0) {
      this.illustration.rotate.x = this.controls.rotationX;
      this.illustration.rotate.y = this.controls.rotationY;
      this.illustration.rotate.z = this.controls.rotationZ;
    }

    // Apply pan controls
    if (this.controls.panX !== 0 || this.controls.panY !== 0) {
      this.illustration.translate.x = this.controls.panX;
      this.illustration.translate.y = this.controls.panY;
    }

    // Apply zoom
    if (this.controls.zoom !== 1) {
      this.illustration.zoom = this.controls.zoom;
    }

    // Animate individual shapes if needed
    this.animateShapes(now);

    // Render the illustration
    const renderStart = performance.now();
    this.illustration.updateRenderGraph();
    this.performance.renderTime = performance.now() - renderStart;

    // Update performance metrics
    this.performance.shapeCount = this.shapes.size;

    // Emit render event
    this.emit({
      type: 'render',
      data: this.performance,
      timestamp: Date.now(),
      target: this
    });

    // Continue animation loop
    if (this.isAnimating) {
      this.animationFrameId = requestAnimationFrame(() => this.render());
    }
  }

  /**
   * Animate individual shapes
   */
  private animateShapes(currentTime: number): void {
    for (const [id, shapeInfo] of this.shapes) {
      if (!shapeInfo.visible) continue;

      // Example: Subtle floating animation for shapes
      if (this.controls.autoRotate) {
        const floatOffset = Math.sin(currentTime * 0.001 + parseInt(id.slice(-2), 16) * 0.1) * 2;
        shapeInfo.zdogShape.translate.z = shapeInfo.transform.z + floatOffset;
      }

      // Apply shape-specific transforms
      if (shapeInfo.transform.rotateX !== 0 || shapeInfo.transform.rotateY !== 0 || shapeInfo.transform.rotateZ !== 0) {
        shapeInfo.zdogShape.rotate.set({
          x: shapeInfo.transform.rotateX,
          y: shapeInfo.transform.rotateY,
          z: shapeInfo.transform.rotateZ
        });
      }

      if (shapeInfo.transform.scale !== 1) {
        if (typeof shapeInfo.zdogShape.scale === 'object') {
          shapeInfo.zdogShape.scale.set({
            x: shapeInfo.transform.scale,
            y: shapeInfo.transform.scale,
            z: shapeInfo.transform.scale
          });
        } else {
          (shapeInfo.zdogShape as any).scale = shapeInfo.transform.scale;
        }
      }
    }
  }

  /**
   * Start animation loop
   */
  public startAnimation(): void {
    if (!this.isAnimating) {
      this.isAnimating = true;
      this.render();
    }
  }

  /**
   * Stop animation loop
   */
  public stopAnimation(): void {
    this.isAnimating = false;
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  // Event system implementation
  public on<T extends ViewerEventUnion>(eventType: T['type'], handler: EventHandler<T>): void {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType]!.push(handler as EventHandler);
  }

  public off<T extends ViewerEventUnion>(eventType: T['type'], handler: EventHandler<T>): void {
    const handlers = this.eventHandlers[eventType];
    if (handlers) {
      const index = handlers.indexOf(handler as EventHandler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  public emit<T extends ViewerEventUnion>(event: T): void {
    const handlers = this.eventHandlers[event.type];
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error('Error in event handler:', error);
        }
      });
    }
  }

  // Public API methods
  public getState(): ViewerState {
    return {
      isInitialized: this.isInitialized,
      isLoading: this.isLoading,
      isAnimating: this.isAnimating,
      shapes: new Map(this.shapes),
      controls: { ...this.controls },
      performance: { ...this.performance },
      lastRenderTime: this.lastFrameTime,
      svgDocument: this.svgDocument
    };
  }

  public getShape(id: string): ShapeInfo | undefined {
    return this.shapes.get(id);
  }

  public getAllShapes(): ShapeInfo[] {
    return Array.from(this.shapes.values());
  }

  public setControls(controls: Partial<ViewerControls>): void {
    Object.assign(this.controls, controls);
  }

  public resetView(): void {
    this.illustration.rotate.set({ x: 0, y: 0, z: 0 });
    this.illustration.translate.set({ x: 0, y: 0, z: 0 });
    this.controls.zoom = 1;
    this.controls.panX = 0;
    this.controls.panY = 0;
    this.render();
  }

  public updateShape(id: string, properties: Partial<ShapeInfo>): void {
    const shape = this.shapes.get(id);
    if (shape) {
      Object.assign(shape, properties);
      this.emit({
        type: 'shapeUpdated',
        data: { shape, action: 'update' },
        timestamp: Date.now(),
        target: this
      });
    }
  }

  public removeShape(id: string): boolean {
    const shape = this.shapes.get(id);
    if (shape) {
      shape.zdogShape.remove();
      this.shapes.delete(id);
      this.emit({
        type: 'shapeRemoved',
        data: { shape, action: 'remove' },
        timestamp: Date.now(),
        target: this
      });
      return true;
    }
    return false;
  }

  public async exportImage(options: ExportOptions = { format: 'png' }): Promise<Blob> {
    if (!this.exporter) {
      throw new ZdogViewerError('Exporter not initialized');
    }

    const validationErrors = ZdogExporter.validateExportOptions(options);
    if (validationErrors.length > 0) {
      throw new ZdogViewerError(`Invalid export options: ${validationErrors.join(', ')}`);
    }

    try {
      const blob = await this.exporter.exportImage(options);

      this.emit({
        type: 'export',
        data: { format: options.format || 'png', size: blob.size, blob },
        timestamp: Date.now(),
        target: this
      });

      return blob;
    } catch (error) {
      throw new ZdogViewerError(
        `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  public installPlugin(plugin: ZdogPlugin): void {
    if (this.plugins.has(plugin.name)) {
      throw new ZdogViewerError(`Plugin ${plugin.name} is already installed`);
    }
    
    plugin.install(this);
    this.plugins.set(plugin.name, plugin);
  }

  public uninstallPlugin(pluginName: string): void {
    const plugin = this.plugins.get(pluginName);
    if (plugin) {
      if (plugin.uninstall) {
        plugin.uninstall(this);
      }
      this.plugins.delete(pluginName);
    }
  }

  public destroy(): void {
    this.stopAnimation();
    this.clearShapes();

    // Clean up interaction listeners
    if (this.cleanupInteractionListeners) {
      this.cleanupInteractionListeners();
    }

    this.eventHandlers = {};
    this.plugins.clear();
    this.isInitialized = false;
  }
}
