/**
 * Basic usage examples for the Zdog 3D SVG Viewer
 * Demonstrates common use cases and integration patterns
 */

import { ZdogSVGViewer } from '../ZdogViewer';
import { ZdogControls } from '../ZdogControls';
import type { 
  ZdogViewerOptions, 
  SVGImportOptions, 
  ExportOptions,
  ViewerControls 
} from '../types/viewer-types';

// Example 1: Basic Viewer Setup
export function createBasicViewer(container: HTMLElement): ZdogSVGViewer {
  const options: ZdogViewerOptions = {
    container,
    width: 800,
    height: 600,
    backgroundColor: '#f0f0f0',
    dragRotate: true,
    autoResize: true,
    onReady: (viewer) => {
      console.log('Viewer is ready!', viewer);
    },
    onError: (error) => {
      console.error('Viewer error:', error);
    }
  };

  return new ZdogSVGViewer(options);
}

// Example 2: Loading SVG Content
export async function loadSampleSVG(viewer: ZdogSVGViewer): Promise<void> {
  const svgContent = `
    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      <!-- Rectangle -->
      <rect x="20" y="20" width="60" height="40" fill="#ff6b6b" stroke="#d63031" stroke-width="2"/>
      
      <!-- Circle -->
      <circle cx="150" cy="50" r="25" fill="#4ecdc4" stroke="#00b894" stroke-width="2"/>
      
      <!-- Path - Triangle -->
      <path d="M100,120 L140,180 L60,180 Z" fill="#fdcb6e" stroke="#e17055" stroke-width="2"/>
      
      <!-- Complex path - Star -->
      <path d="M150,120 L160,140 L180,140 L166,154 L172,174 L150,162 L128,174 L134,154 L120,140 L140,140 Z" 
            fill="#a29bfe" stroke="#6c5ce7" stroke-width="2"/>
      
      <!-- Polyline -->
      <polyline points="20,160 40,140 60,160 80,140 100,160" 
                fill="none" stroke="#fd79a8" stroke-width="3"/>
    </svg>
  `;

  const importOptions: SVGImportOptions = {
    depth: 25,
    strokeWidth: 3,
    centerOrigin: true,
    flipY: true,
    scale: 1.2,
    materialOptions: {
      defaultColor: '#333',
      enableBackface: true,
      backfaceColor: '#666'
    }
  };

  try {
    await viewer.loadSVG(svgContent, importOptions);
    console.log('SVG loaded successfully!');
  } catch (error) {
    console.error('Failed to load SVG:', error);
  }
}

// Example 3: Animation and Controls
export function setupAnimationControls(viewer: ZdogSVGViewer): void {
  // Set up auto-rotation
  const controls: Partial<ViewerControls> = {
    autoRotate: true,
    rotationSpeed: 0.02,
    zoom: 1.5
  };

  viewer.setControls(controls);
  viewer.startAnimation();

  // Add event listeners
  viewer.on('render', (event) => {
    // Monitor performance
    if (event.data.fps < 30) {
      console.warn('Low FPS detected:', event.data.fps);
    }
  });

  viewer.on('load', (event) => {
    console.log(`Loaded ${event.data.shapeCount} shapes in ${event.data.parseTime}ms`);
  });
}

// Example 4: Interactive Controls UI
export function createControlsUI(viewer: ZdogSVGViewer, container: HTMLElement): ZdogControls {
  const controlsContainer = document.createElement('div');
  controlsContainer.className = 'zdog-controls-container';
  container.appendChild(controlsContainer);

  const controls = new ZdogControls({
    container: controlsContainer,
    viewer,
    showLabels: true,
    compactMode: false,
    config: {
      showDepthControl: true,
      showRotationControls: true,
      showZoomControls: true,
      showExportButton: true,
      showResetButton: true,
      showPerformanceMetrics: true,
      customButtons: [
        {
          id: 'randomize',
          label: 'Randomize',
          icon: '🎲',
          tooltip: 'Randomize shape positions',
          onClick: (viewer) => {
            randomizeShapes(viewer);
          }
        }
      ]
    }
  });

  return controls;
}

// Example 5: Shape Manipulation
export function randomizeShapes(viewer: ZdogSVGViewer): void {
  const shapes = viewer.getAllShapes();
  
  shapes.forEach(shape => {
    const randomTransform = {
      x: (Math.random() - 0.5) * 100,
      y: (Math.random() - 0.5) * 100,
      z: (Math.random() - 0.5) * 50,
      rotateX: Math.random() * Math.PI * 2,
      rotateY: Math.random() * Math.PI * 2,
      rotateZ: Math.random() * Math.PI * 2,
      scale: 0.5 + Math.random() * 1.5
    };

    viewer.updateShape(shape.id, {
      transform: randomTransform
    });
  });
}

// Example 6: Export Functionality
export async function exportViewer(viewer: ZdogSVGViewer): Promise<void> {
  const exportOptions: ExportOptions = {
    format: 'png',
    width: 1920,
    height: 1080,
    backgroundColor: '#ffffff',
    transparent: false,
    scale: 2, // High DPI
    quality: 0.95
  };

  try {
    const blob = await viewer.exportImage(exportOptions);
    
    // Create download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `zdog-export-${Date.now()}.png`;
    a.click();
    
    // Cleanup
    URL.revokeObjectURL(url);
    
    console.log('Export completed successfully!');
  } catch (error) {
    console.error('Export failed:', error);
  }
}

// Example 7: Multiple Export Formats
export async function exportMultipleFormats(viewer: ZdogSVGViewer): Promise<void> {
  const formats: Array<{ format: 'png' | 'svg' | 'webp' | 'jpeg', quality?: number }> = [
    { format: 'png' },
    { format: 'svg' },
    { format: 'webp', quality: 0.9 },
    { format: 'jpeg', quality: 0.85 }
  ];

  for (const { format, quality } of formats) {
    try {
      const blob = await viewer.exportImage({
        format,
        quality,
        width: 800,
        height: 600
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `export-${Date.now()}.${format}`;
      a.click();
      URL.revokeObjectURL(url);

      console.log(`Exported ${format} successfully`);
    } catch (error) {
      console.error(`Failed to export ${format}:`, error);
    }
  }
}

// Example 8: Event Handling
export function setupEventHandlers(viewer: ZdogSVGViewer): void {
  // Performance monitoring
  viewer.on('render', (event) => {
    const metrics = event.data;
    updatePerformanceDisplay(metrics);
  });

  // Shape events
  viewer.on('shapeAdded', (event) => {
    console.log('Shape added:', event.data.shape.id);
  });

  viewer.on('shapeRemoved', (event) => {
    console.log('Shape removed:', event.data.shape.id);
  });

  // Interaction events
  viewer.on('interactionStart', (event) => {
    console.log('Interaction started:', event.data.type);
  });

  viewer.on('interactionEnd', (event) => {
    console.log('Interaction ended:', event.data.type);
  });

  // Error handling
  viewer.on('error', (event) => {
    showErrorMessage(event.data.error.message);
  });
}

// Helper function to update performance display
function updatePerformanceDisplay(metrics: any): void {
  const display = document.getElementById('performance-display');
  if (display) {
    display.innerHTML = `
      <div>FPS: ${metrics.fps}</div>
      <div>Shapes: ${metrics.shapeCount}</div>
      <div>Render Time: ${metrics.renderTime.toFixed(2)}ms</div>
    `;
  }
}

// Helper function to show error messages
function showErrorMessage(message: string): void {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.textContent = message;
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ff4757;
    color: white;
    padding: 12px 16px;
    border-radius: 4px;
    z-index: 1000;
  `;
  
  document.body.appendChild(errorDiv);
  
  setTimeout(() => {
    document.body.removeChild(errorDiv);
  }, 5000);
}

// Example 9: Complete Integration
export async function createCompleteExample(containerId: string): Promise<ZdogSVGViewer> {
  const container = document.getElementById(containerId);
  if (!container) {
    throw new Error(`Container with id '${containerId}' not found`);
  }

  // Create viewer
  const viewer = createBasicViewer(container);

  // Setup controls
  createControlsUI(viewer, container);

  // Setup event handlers
  setupEventHandlers(viewer);

  // Load sample content
  await loadSampleSVG(viewer);

  // Setup animation
  setupAnimationControls(viewer);

  console.log('Complete Zdog viewer example created successfully!');
  return viewer;
}

// Example 10: Cleanup
export function cleanupViewer(viewer: ZdogSVGViewer): void {
  // Stop animation
  viewer.stopAnimation();

  // Remove all shapes
  const shapes = viewer.getAllShapes();
  shapes.forEach(shape => {
    viewer.removeShape(shape.id);
  });

  // Destroy viewer
  viewer.destroy();

  console.log('Viewer cleaned up successfully');
}
