/**
 * Zdog Controls - Interactive UI controls for the 3D SVG viewer
 * Provides TypeScript-safe controls for depth, rotation, zoom, and other viewer properties
 */

import type { ZdogSVGViewer } from './ZdogViewer';
import type { 
  ViewerControls, 
  ToolbarConfig, 
  ToolbarButton,
  PerformanceMetrics 
} from './types/viewer-types';

export interface ControlsOptions {
  container: HTMLElement;
  viewer: ZdogSVGViewer;
  config?: Partial<ToolbarConfig>;
  showLabels?: boolean;
  compactMode?: boolean;
}

export class ZdogControls {
  private container: HTMLElement;
  private viewer: ZdogSVGViewer;
  private config: ToolbarConfig;
  private controlElements: Map<string, HTMLElement> = new Map();
  private isCompact: boolean;
  private showLabels: boolean;

  constructor(options: ControlsOptions) {
    this.container = options.container;
    this.viewer = options.viewer;
    this.isCompact = options.compactMode ?? false;
    this.showLabels = options.showLabels ?? true;
    
    this.config = {
      showDepthControl: true,
      showRotationControls: true,
      showZoomControls: true,
      showExportButton: true,
      showResetButton: true,
      showPerformanceMetrics: false,
      customButtons: [],
      ...options.config
    };

    this.initialize();
    this.setupEventListeners();
  }

  /**
   * Initialize the controls UI
   */
  private initialize(): void {
    this.container.className = `zdog-controls ${this.isCompact ? 'compact' : 'full'}`;
    this.container.innerHTML = ''; // Clear existing content

    // Create main toolbar
    const toolbar = this.createToolbar();
    this.container.appendChild(toolbar);

    // Create control panels
    if (this.config.showDepthControl) {
      const depthPanel = this.createDepthControls();
      this.container.appendChild(depthPanel);
    }

    if (this.config.showRotationControls) {
      const rotationPanel = this.createRotationControls();
      this.container.appendChild(rotationPanel);
    }

    if (this.config.showZoomControls) {
      const zoomPanel = this.createZoomControls();
      this.container.appendChild(zoomPanel);
    }

    if (this.config.showPerformanceMetrics) {
      const metricsPanel = this.createPerformanceMetrics();
      this.container.appendChild(metricsPanel);
    }

    // Apply styles
    this.applyStyles();
  }

  /**
   * Create main toolbar with action buttons
   */
  private createToolbar(): HTMLElement {
    const toolbar = document.createElement('div');
    toolbar.className = 'zdog-toolbar';

    // Reset view button
    if (this.config.showResetButton) {
      const resetBtn = this.createButton({
        id: 'reset',
        label: 'Reset View',
        icon: '🔄',
        tooltip: 'Reset camera to default position',
        onClick: () => this.viewer.resetView()
      });
      toolbar.appendChild(resetBtn);
    }

    // Export button
    if (this.config.showExportButton) {
      const exportBtn = this.createButton({
        id: 'export',
        label: 'Export',
        icon: '💾',
        tooltip: 'Export as image',
        onClick: () => this.handleExport()
      });
      toolbar.appendChild(exportBtn);
    }

    // Custom buttons
    if (this.config.customButtons) {
      this.config.customButtons.forEach(buttonConfig => {
        const customBtn = this.createButton(buttonConfig);
        toolbar.appendChild(customBtn);
      });
    }

    return toolbar;
  }

  /**
   * Create depth/extrusion controls
   */
  private createDepthControls(): HTMLElement {
    const panel = this.createControlPanel('Depth Controls');
    
    // Depth slider
    const depthControl = this.createSliderControl({
      id: 'depth',
      label: 'Extrusion Depth',
      min: 0,
      max: 100,
      value: 10,
      step: 1,
      unit: 'px',
      onChange: (value) => this.handleDepthChange(value)
    });
    panel.appendChild(depthControl);

    // Stroke width slider
    const strokeControl = this.createSliderControl({
      id: 'strokeWidth',
      label: 'Stroke Width',
      min: 1,
      max: 50,
      value: 2,
      step: 0.5,
      unit: 'px',
      onChange: (value) => this.handleStrokeWidthChange(value)
    });
    panel.appendChild(strokeControl);

    return panel;
  }

  /**
   * Create rotation controls
   */
  private createRotationControls(): HTMLElement {
    const panel = this.createControlPanel('Rotation Controls');

    // Auto-rotate toggle
    const autoRotateToggle = this.createToggleControl({
      id: 'autoRotate',
      label: 'Auto Rotate',
      checked: false,
      onChange: (checked) => this.handleAutoRotateChange(checked)
    });
    panel.appendChild(autoRotateToggle);

    // Rotation speed slider
    const speedControl = this.createSliderControl({
      id: 'rotationSpeed',
      label: 'Rotation Speed',
      min: 0.001,
      max: 0.1,
      value: 0.01,
      step: 0.001,
      unit: 'rad/frame',
      onChange: (value) => this.handleRotationSpeedChange(value)
    });
    panel.appendChild(speedControl);

    // Manual rotation controls
    const manualRotation = this.createManualRotationControls();
    panel.appendChild(manualRotation);

    return panel;
  }

  /**
   * Create zoom and pan controls
   */
  private createZoomControls(): HTMLElement {
    const panel = this.createControlPanel('View Controls');

    // Zoom slider
    const zoomControl = this.createSliderControl({
      id: 'zoom',
      label: 'Zoom',
      min: 0.1,
      max: 5,
      value: 1,
      step: 0.1,
      unit: 'x',
      onChange: (value) => this.handleZoomChange(value)
    });
    panel.appendChild(zoomControl);

    // Pan controls
    const panControls = this.createPanControls();
    panel.appendChild(panControls);

    return panel;
  }

  /**
   * Create performance metrics display
   */
  private createPerformanceMetrics(): HTMLElement {
    const panel = this.createControlPanel('Performance');
    panel.id = 'performance-metrics';

    const metricsContainer = document.createElement('div');
    metricsContainer.className = 'metrics-container';

    // FPS display
    const fpsDisplay = document.createElement('div');
    fpsDisplay.className = 'metric-item';
    fpsDisplay.innerHTML = '<span class="metric-label">FPS:</span> <span class="metric-value" id="fps-value">0</span>';
    metricsContainer.appendChild(fpsDisplay);

    // Shape count display
    const shapeDisplay = document.createElement('div');
    shapeDisplay.className = 'metric-item';
    shapeDisplay.innerHTML = '<span class="metric-label">Shapes:</span> <span class="metric-value" id="shapes-value">0</span>';
    metricsContainer.appendChild(shapeDisplay);

    // Render time display
    const renderDisplay = document.createElement('div');
    renderDisplay.className = 'metric-item';
    renderDisplay.innerHTML = '<span class="metric-label">Render:</span> <span class="metric-value" id="render-value">0ms</span>';
    metricsContainer.appendChild(renderDisplay);

    panel.appendChild(metricsContainer);
    return panel;
  }

  /**
   * Create a control panel with title
   */
  private createControlPanel(title: string): HTMLElement {
    const panel = document.createElement('div');
    panel.className = 'zdog-control-panel';

    if (this.showLabels && !this.isCompact) {
      const titleElement = document.createElement('h4');
      titleElement.className = 'panel-title';
      titleElement.textContent = title;
      panel.appendChild(titleElement);
    }

    return panel;
  }

  /**
   * Create a slider control
   */
  private createSliderControl(options: {
    id: string;
    label: string;
    min: number;
    max: number;
    value: number;
    step: number;
    unit?: string;
    onChange: (value: number) => void;
  }): HTMLElement {
    const container = document.createElement('div');
    container.className = 'slider-control';

    if (this.showLabels) {
      const label = document.createElement('label');
      label.textContent = options.label;
      label.htmlFor = options.id;
      container.appendChild(label);
    }

    const sliderContainer = document.createElement('div');
    sliderContainer.className = 'slider-container';

    const slider = document.createElement('input');
    slider.type = 'range';
    slider.id = options.id;
    slider.min = options.min.toString();
    slider.max = options.max.toString();
    slider.value = options.value.toString();
    slider.step = options.step.toString();
    slider.className = 'slider';

    const valueDisplay = document.createElement('span');
    valueDisplay.className = 'value-display';
    valueDisplay.textContent = `${options.value}${options.unit || ''}`;

    slider.addEventListener('input', (e) => {
      const value = parseFloat((e.target as HTMLInputElement).value);
      valueDisplay.textContent = `${value}${options.unit || ''}`;
      options.onChange(value);
    });

    sliderContainer.appendChild(slider);
    sliderContainer.appendChild(valueDisplay);
    container.appendChild(sliderContainer);

    this.controlElements.set(options.id, slider);
    return container;
  }

  /**
   * Create a toggle control
   */
  private createToggleControl(options: {
    id: string;
    label: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
  }): HTMLElement {
    const container = document.createElement('div');
    container.className = 'toggle-control';

    const label = document.createElement('label');
    label.className = 'toggle-label';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.id = options.id;
    checkbox.checked = options.checked;
    checkbox.className = 'toggle-checkbox';

    const toggleSwitch = document.createElement('span');
    toggleSwitch.className = 'toggle-switch';

    const labelText = document.createElement('span');
    labelText.textContent = options.label;

    checkbox.addEventListener('change', (e) => {
      options.onChange((e.target as HTMLInputElement).checked);
    });

    label.appendChild(checkbox);
    label.appendChild(toggleSwitch);
    if (this.showLabels) {
      label.appendChild(labelText);
    }
    container.appendChild(label);

    this.controlElements.set(options.id, checkbox);
    return container;
  }

  /**
   * Create a button
   */
  private createButton(config: ToolbarButton): HTMLElement {
    const button = document.createElement('button');
    button.className = 'zdog-button';
    button.id = config.id;
    button.disabled = config.disabled ?? false;
    button.style.display = config.visible === false ? 'none' : 'block';

    if (config.tooltip) {
      button.title = config.tooltip;
    }

    // Button content
    const content = document.createElement('span');
    if (config.icon) {
      content.innerHTML = `${config.icon} ${this.showLabels && !this.isCompact ? config.label : ''}`;
    } else {
      content.textContent = config.label;
    }
    button.appendChild(content);

    button.addEventListener('click', () => {
      if (!button.disabled) {
        config.onClick(this.viewer);
      }
    });

    this.controlElements.set(config.id, button);
    return button;
  }

  /**
   * Create manual rotation controls
   */
  private createManualRotationControls(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'manual-rotation';

    if (this.showLabels) {
      const title = document.createElement('span');
      title.textContent = 'Manual Rotation';
      title.className = 'control-subtitle';
      container.appendChild(title);
    }

    const axes = ['X', 'Y', 'Z'];
    axes.forEach(axis => {
      const axisControl = this.createSliderControl({
        id: `rotate${axis}`,
        label: `Rotate ${axis}`,
        min: -Math.PI,
        max: Math.PI,
        value: 0,
        step: 0.1,
        unit: ' rad',
        onChange: (value) => this.handleManualRotation(axis.toLowerCase(), value)
      });
      container.appendChild(axisControl);
    });

    return container;
  }

  /**
   * Create pan controls
   */
  private createPanControls(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'pan-controls';

    if (this.showLabels) {
      const title = document.createElement('span');
      title.textContent = 'Pan';
      title.className = 'control-subtitle';
      container.appendChild(title);
    }

    const panX = this.createSliderControl({
      id: 'panX',
      label: 'Pan X',
      min: -200,
      max: 200,
      value: 0,
      step: 1,
      unit: 'px',
      onChange: (value) => this.handlePanChange('x', value)
    });

    const panY = this.createSliderControl({
      id: 'panY',
      label: 'Pan Y',
      min: -200,
      max: 200,
      value: 0,
      step: 1,
      unit: 'px',
      onChange: (value) => this.handlePanChange('y', value)
    });

    container.appendChild(panX);
    container.appendChild(panY);

    return container;
  }

  /**
   * Setup event listeners for viewer updates
   */
  private setupEventListeners(): void {
    // Listen for render events to update performance metrics
    this.viewer.on('render', (event) => {
      this.updatePerformanceMetrics(event.data);
    });

    // Listen for shape changes to update shape count
    this.viewer.on('shapeAdded', () => {
      this.updateShapeCount();
    });

    this.viewer.on('shapeRemoved', () => {
      this.updateShapeCount();
    });
  }

  /**
   * Event handlers
   */
  private handleDepthChange(value: number): void {
    // TODO: Update depth for all shapes
    console.log('Depth changed:', value);
  }

  private handleStrokeWidthChange(value: number): void {
    // TODO: Update stroke width for all shapes
    console.log('Stroke width changed:', value);
  }

  private handleAutoRotateChange(checked: boolean): void {
    const controls = this.viewer.getState().controls;
    this.viewer.setControls({ ...controls, autoRotate: checked });
    
    if (checked) {
      this.viewer.startAnimation();
    } else {
      this.viewer.stopAnimation();
    }
  }

  private handleRotationSpeedChange(value: number): void {
    const controls = this.viewer.getState().controls;
    this.viewer.setControls({ ...controls, rotationSpeed: value });
  }

  private handleZoomChange(value: number): void {
    const controls = this.viewer.getState().controls;
    this.viewer.setControls({ ...controls, zoom: value });
  }

  private handleManualRotation(axis: string, value: number): void {
    const controls = this.viewer.getState().controls;
    const rotationKey = `rotation${axis.toUpperCase()}` as keyof ViewerControls;
    this.viewer.setControls({ ...controls, [rotationKey]: value });
  }

  private handlePanChange(axis: string, value: number): void {
    const controls = this.viewer.getState().controls;
    const panKey = `pan${axis.toUpperCase()}` as keyof ViewerControls;
    this.viewer.setControls({ ...controls, [panKey]: value });
  }

  private async handleExport(): Promise<void> {
    try {
      const blob = await this.viewer.exportImage({ format: 'png' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `zdog-export-${Date.now()}.png`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  }

  /**
   * Update performance metrics display
   */
  private updatePerformanceMetrics(metrics: PerformanceMetrics): void {
    const fpsElement = document.getElementById('fps-value');
    const renderElement = document.getElementById('render-value');
    
    if (fpsElement) {
      fpsElement.textContent = metrics.fps.toString();
    }
    
    if (renderElement) {
      renderElement.textContent = `${metrics.renderTime.toFixed(2)}ms`;
    }
  }

  /**
   * Update shape count display
   */
  private updateShapeCount(): void {
    const shapesElement = document.getElementById('shapes-value');
    if (shapesElement) {
      const shapeCount = this.viewer.getAllShapes().length;
      shapesElement.textContent = shapeCount.toString();
    }
  }

  /**
   * Apply CSS styles to controls
   */
  private applyStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .zdog-controls {
        background: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 12px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        max-width: 300px;
      }

      .zdog-controls.compact {
        padding: 8px;
        max-width: 200px;
      }

      .zdog-toolbar {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;
        flex-wrap: wrap;
      }

      .zdog-button {
        background: #007acc;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        cursor: pointer;
        font-size: 12px;
        transition: background-color 0.2s;
      }

      .zdog-button:hover:not(:disabled) {
        background: #005a9e;
      }

      .zdog-button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .zdog-control-panel {
        margin-bottom: 16px;
        padding: 8px;
        background: white;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
      }

      .panel-title {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .slider-control {
        margin-bottom: 12px;
      }

      .slider-control label {
        display: block;
        margin-bottom: 4px;
        font-weight: 500;
        color: #555;
      }

      .slider-container {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .slider {
        flex: 1;
        height: 4px;
        border-radius: 2px;
        background: #ddd;
        outline: none;
        -webkit-appearance: none;
      }

      .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #007acc;
        cursor: pointer;
      }

      .value-display {
        min-width: 60px;
        text-align: right;
        font-family: monospace;
        font-size: 12px;
        color: #666;
      }

      .toggle-control {
        margin-bottom: 12px;
      }

      .toggle-label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
      }

      .toggle-checkbox {
        display: none;
      }

      .toggle-switch {
        width: 40px;
        height: 20px;
        background: #ccc;
        border-radius: 10px;
        position: relative;
        transition: background-color 0.2s;
      }

      .toggle-switch::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: white;
        top: 2px;
        left: 2px;
        transition: transform 0.2s;
      }

      .toggle-checkbox:checked + .toggle-switch {
        background: #007acc;
      }

      .toggle-checkbox:checked + .toggle-switch::after {
        transform: translateX(20px);
      }

      .metrics-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
      }

      .metric-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 8px;
        background: #f9f9f9;
        border-radius: 4px;
        font-size: 12px;
      }

      .metric-label {
        font-weight: 500;
        color: #666;
      }

      .metric-value {
        font-family: monospace;
        color: #333;
      }

      .control-subtitle {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #666;
        margin-bottom: 8px;
      }
    `;

    if (!document.head.querySelector('#zdog-controls-styles')) {
      style.id = 'zdog-controls-styles';
      document.head.appendChild(style);
    }
  }

  /**
   * Update control values from viewer state
   */
  public updateFromViewerState(): void {
    const state = this.viewer.getState();
    
    // Update control elements to match viewer state
    const autoRotateCheckbox = this.controlElements.get('autoRotate') as HTMLInputElement;
    if (autoRotateCheckbox) {
      autoRotateCheckbox.checked = state.controls.autoRotate;
    }

    const rotationSpeedSlider = this.controlElements.get('rotationSpeed') as HTMLInputElement;
    if (rotationSpeedSlider) {
      rotationSpeedSlider.value = state.controls.rotationSpeed.toString();
    }

    // Update other controls as needed
  }

  /**
   * Destroy controls and clean up
   */
  public destroy(): void {
    this.controlElements.clear();
    this.container.innerHTML = '';
  }
}
