<template>
  <div class="zdog-test-view">
    <div class="header">
      <h1>Zdog 3D SVG Viewer Test</h1>
      <p>Testing the Zdog 3D SVG viewer implementation</p>
    </div>

    <div class="test-container">
      <div class="controls-panel">
        <h3>Test Controls</h3>
        
        <div class="control-group">
          <label>Test SVG:</label>
          <select v-model="selectedTest" @change="loadTestSVG">
            <option value="simple">Simple Shapes</option>
            <option value="complex">Complex Path</option>
            <option value="mixed">Mixed Elements</option>
            <option value="custom">Custom SVG</option>
          </select>
        </div>

        <div v-if="selectedTest === 'custom'" class="control-group">
          <label>Custom SVG:</label>
          <textarea 
            v-model="customSvg" 
            rows="8" 
            placeholder="Enter your SVG content here..."
            @input="loadCustomSVG"
          ></textarea>
        </div>

        <div class="control-group">
          <label>Depth:</label>
          <input 
            type="range" 
            min="5" 
            max="50" 
            v-model="depth" 
            @input="updateImportOptions"
          />
          <span>{{ depth }}px</span>
        </div>

        <div class="control-group">
          <label>Stroke Width:</label>
          <input 
            type="range" 
            min="1" 
            max="10" 
            v-model="strokeWidth" 
            @input="updateImportOptions"
          />
          <span>{{ strokeWidth }}px</span>
        </div>

        <div class="control-group">
          <button @click="exportPNG" class="export-btn">Export PNG</button>
          <button @click="exportSVG" class="export-btn">Export SVG</button>
        </div>

        <div class="status-panel">
          <h4>Status</h4>
          <div class="status-item">
            <span>Shapes: {{ shapeCount }}</span>
          </div>
          <div class="status-item">
            <span>FPS: {{ fps }}</span>
          </div>
          <div v-if="error" class="error-message">
            {{ error }}
          </div>
        </div>
      </div>

      <div class="viewer-container">
        <ZdogViewer
          ref="zdogViewerRef"
          :svg-content="currentSvg"
          :width="800"
          :height="600"
          :show-controls="true"
          :compact-controls="false"
          :auto-start="true"
          :import-options="importOptions"
          @viewer-ready="handleViewerReady"
          @svg-loaded="handleSvgLoaded"
          @error="handleError"
          @performance-update="handlePerformanceUpdate"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import ZdogViewer from '@/components/ZdogViewer.vue'
import type { ZdogSVGViewer } from '@/viewers/zdog/ZdogViewer'
import type { SVGImportOptions, PerformanceMetrics, ZdogViewerError } from '@/viewers/zdog/types/viewer-types'

// Component state
const zdogViewerRef = ref<InstanceType<typeof ZdogViewer> | null>(null)
const selectedTest = ref('simple')
const customSvg = ref('')
const currentSvg = ref('')
const depth = ref(20)
const strokeWidth = ref(2)
const shapeCount = ref(0)
const fps = ref(0)
const error = ref('')

// Import options
const importOptions = reactive<SVGImportOptions>({
  depth: 20,
  strokeWidth: 2,
  centerOrigin: true,
  flipY: true,
  scale: 1
})

// Test SVG content
const testSvgs = {
  simple: `
    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      <rect x="20" y="20" width="60" height="40" fill="#ff6b6b" stroke="#d63031" stroke-width="2"/>
      <circle cx="150" cy="50" r="25" fill="#4ecdc4" stroke="#00b894" stroke-width="2"/>
      <line x1="20" y1="100" x2="180" y2="100" stroke="#fdcb6e" stroke-width="3"/>
    </svg>
  `,
  complex: `
    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      <path d="M100,20 Q150,50 100,80 Q50,50 100,20 Z" fill="#a29bfe" stroke="#6c5ce7" stroke-width="2"/>
      <path d="M50,120 C50,120 100,100 150,120 S200,140 150,160 C100,180 50,160 50,120 Z" 
            fill="none" stroke="#fd79a8" stroke-width="3"/>
      <ellipse cx="100" cy="150" rx="40" ry="20" fill="#fdcb6e" stroke="#e17055" stroke-width="2"/>
    </svg>
  `,
  mixed: `
    <svg viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(50,50)">
        <rect x="0" y="0" width="40" height="40" fill="#ff6b6b"/>
        <circle cx="60" cy="20" r="15" fill="#4ecdc4"/>
      </g>
      <polygon points="150,50 170,90 130,90" fill="#fdcb6e" stroke="#e17055" stroke-width="2"/>
      <polyline points="200,50 220,70 240,50 260,70 280,50" 
                fill="none" stroke="#a29bfe" stroke-width="3"/>
      <line x1="50" y1="150" x2="250" y2="150" stroke="#fd79a8" stroke-width="2"/>
      <ellipse cx="150" cy="200" rx="60" ry="30" fill="none" stroke="#00b894" stroke-width="2"/>
    </svg>
  `
}

// Event handlers
const handleViewerReady = (viewer: ZdogSVGViewer) => {
  console.log('Zdog viewer ready:', viewer)
  error.value = ''
}

const handleSvgLoaded = (count: number) => {
  shapeCount.value = count
  error.value = ''
  console.log('SVG loaded with', count, 'shapes')
}

const handleError = (err: ZdogViewerError) => {
  error.value = err.message
  console.error('Zdog viewer error:', err)
}

const handlePerformanceUpdate = (metrics: PerformanceMetrics) => {
  fps.value = metrics.fps
}

// Control functions
const loadTestSVG = () => {
  if (selectedTest.value !== 'custom') {
    currentSvg.value = testSvgs[selectedTest.value as keyof typeof testSvgs]
  }
}

const loadCustomSVG = () => {
  if (selectedTest.value === 'custom') {
    currentSvg.value = customSvg.value
  }
}

const updateImportOptions = () => {
  importOptions.depth = depth.value
  importOptions.strokeWidth = strokeWidth.value
}

const exportPNG = async () => {
  if (zdogViewerRef.value) {
    try {
      const blob = await zdogViewerRef.value.exportImage('png')
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `zdog-test-${Date.now()}.png`
      a.click()
      URL.revokeObjectURL(url)
    } catch (err) {
      error.value = `Export failed: ${err instanceof Error ? err.message : 'Unknown error'}`
    }
  }
}

const exportSVG = async () => {
  if (zdogViewerRef.value) {
    try {
      const blob = await zdogViewerRef.value.exportImage('svg')
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `zdog-test-${Date.now()}.svg`
      a.click()
      URL.revokeObjectURL(url)
    } catch (err) {
      error.value = `Export failed: ${err instanceof Error ? err.message : 'Unknown error'}`
    }
  }
}

// Initialize
onMounted(() => {
  loadTestSVG()
})
</script>

<style scoped>
.zdog-test-view {
  @apply h-full flex flex-col bg-gray-50;
}

.header {
  @apply p-6 bg-white border-b border-gray-200;
}

.header h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.header p {
  @apply text-gray-600;
}

.test-container {
  @apply flex-1 flex overflow-hidden;
}

.controls-panel {
  @apply w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto;
}

.controls-panel h3 {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

.control-group {
  @apply mb-4;
}

.control-group label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.control-group select,
.control-group input[type="range"] {
  @apply w-full;
}

.control-group textarea {
  @apply w-full p-2 border border-gray-300 rounded-md resize-vertical;
}

.control-group input[type="range"] {
  @apply mr-2;
}

.export-btn {
  @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 mr-2 mb-2;
}

.status-panel {
  @apply mt-6 p-4 bg-gray-50 rounded-md;
}

.status-panel h4 {
  @apply text-sm font-semibold text-gray-900 mb-2;
}

.status-item {
  @apply text-sm text-gray-600 mb-1;
}

.error-message {
  @apply text-sm text-red-600 bg-red-50 p-2 rounded-md mt-2;
}

.viewer-container {
  @apply flex-1 p-4;
}
</style>
