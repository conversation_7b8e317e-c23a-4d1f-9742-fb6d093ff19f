<template>
  <div class="zdog-viewer-container">
    <!-- Zdog Canvas Container -->
    <div 
      ref="zdogContainer" 
      class="zdog-canvas"
      :style="{ width: `${width}px`, height: `${height}px` }"
    ></div>
    
    <!-- Controls Panel -->
    <div 
      v-if="showControls" 
      ref="controlsContainer" 
      class="zdog-controls-panel"
      :class="{ 'controls-compact': compactControls }"
    ></div>
    
    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <span class="loading-text">Loading 3D SVG...</span>
    </div>
    
    <!-- Error Display -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <h4>Error Loading SVG</h4>
        <p>{{ error.message }}</p>
        <button @click="clearError" class="error-dismiss">Dismiss</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import { ZdogSVGViewer } from '@/viewers/zdog/ZdogViewer'
import { ZdogControls } from '@/viewers/zdog/ZdogControls'
import type { 
  ZdogViewerOptions, 
  SVGImportOptions, 
  ViewerControls,
  PerformanceMetrics,
  ZdogViewerError 
} from '@/viewers/zdog/types/viewer-types'

interface Props {
  svgContent?: string
  width?: number
  height?: number
  showControls?: boolean
  compactControls?: boolean
  autoStart?: boolean
  importOptions?: SVGImportOptions
  viewerOptions?: Partial<ZdogViewerOptions>
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600,
  showControls: true,
  compactControls: false,
  autoStart: true,
  importOptions: () => ({
    depth: 20,
    strokeWidth: 2,
    centerOrigin: true,
    flipY: true
  }),
  viewerOptions: () => ({})
})

const emit = defineEmits<{
  'viewer-ready': [viewer: ZdogSVGViewer]
  'svg-loaded': [shapeCount: number]
  'error': [error: ZdogViewerError]
  'performance-update': [metrics: PerformanceMetrics]
}>()

// Template refs
const zdogContainer = ref<HTMLElement>()
const controlsContainer = ref<HTMLElement>()

// Component state
const isLoading = ref(false)
const error = ref<ZdogViewerError | null>(null)
const viewer = ref<ZdogSVGViewer | null>(null)
const controls = ref<ZdogControls | null>(null)

// Computed properties
const containerStyle = computed(() => ({
  width: `${props.width}px`,
  height: `${props.height}px`
}))

/**
 * Initialize the Zdog viewer
 */
const initializeViewer = async (): Promise<void> => {
  if (!zdogContainer.value) {
    console.error('Zdog container not available')
    return
  }

  try {
    const viewerOptions: ZdogViewerOptions = {
      container: zdogContainer.value,
      width: props.width,
      height: props.height,
      dragRotate: true,
      autoResize: true,
      onReady: (v) => {
        emit('viewer-ready', v)
      },
      onError: (err) => {
        error.value = err
        emit('error', err)
      },
      ...props.viewerOptions
    }

    viewer.value = new ZdogSVGViewer(viewerOptions)
    
    // Setup event listeners
    setupEventListeners()
    
    // Initialize controls if enabled
    if (props.showControls && controlsContainer.value) {
      initializeControls()
    }

    // Load SVG content if provided
    if (props.svgContent && props.autoStart) {
      await loadSVG(props.svgContent)
    }

  } catch (err) {
    const viewerError = err instanceof Error ? 
      new (Error as any)(err.message) : 
      new (Error as any)('Failed to initialize viewer')
    error.value = viewerError
    emit('error', viewerError)
  }
}

/**
 * Initialize controls panel
 */
const initializeControls = (): void => {
  if (!viewer.value || !controlsContainer.value) return

  try {
    controls.value = new ZdogControls({
      container: controlsContainer.value,
      viewer: viewer.value,
      compactMode: props.compactControls,
      config: {
        showDepthControl: true,
        showRotationControls: true,
        showZoomControls: true,
        showExportButton: true,
        showResetButton: true,
        showPerformanceMetrics: true
      }
    })
  } catch (err) {
    console.error('Failed to initialize controls:', err)
  }
}

/**
 * Setup event listeners for the viewer
 */
const setupEventListeners = (): void => {
  if (!viewer.value) return

  viewer.value.on('load', (event) => {
    isLoading.value = false
    emit('svg-loaded', event.data.shapeCount)
  })

  viewer.value.on('error', (event) => {
    isLoading.value = false
    error.value = event.data.error
    emit('error', event.data.error)
  })

  viewer.value.on('render', (event) => {
    emit('performance-update', event.data)
  })
}

/**
 * Load SVG content into the viewer
 */
const loadSVG = async (svgContent: string): Promise<void> => {
  if (!viewer.value) {
    throw new Error('Viewer not initialized')
  }

  isLoading.value = true
  error.value = null

  try {
    await viewer.value.loadSVG(svgContent, props.importOptions)
    viewer.value.startAnimation()
  } catch (err) {
    isLoading.value = false
    throw err
  }
}

/**
 * Clear current error
 */
const clearError = (): void => {
  error.value = null
}

/**
 * Get current viewer instance
 */
const getViewer = (): ZdogSVGViewer | null => {
  return viewer.value
}

/**
 * Get current controls instance
 */
const getControls = (): ZdogControls | null => {
  return controls.value
}

/**
 * Update viewer size
 */
const updateSize = (width: number, height: number): void => {
  if (viewer.value) {
    // The viewer should handle resize automatically via ResizeObserver
    // But we can trigger a manual resize if needed
  }
}

/**
 * Reset viewer to default state
 */
const resetView = (): void => {
  if (viewer.value) {
    viewer.value.resetView()
  }
}

/**
 * Start animation
 */
const startAnimation = (): void => {
  if (viewer.value) {
    viewer.value.startAnimation()
  }
}

/**
 * Stop animation
 */
const stopAnimation = (): void => {
  if (viewer.value) {
    viewer.value.stopAnimation()
  }
}

/**
 * Export current view as image
 */
const exportImage = async (format: 'png' | 'svg' = 'png'): Promise<Blob> => {
  if (!viewer.value) {
    throw new Error('Viewer not initialized')
  }
  
  return await viewer.value.exportImage({ format })
}

// Watch for SVG content changes
watch(() => props.svgContent, async (newContent) => {
  if (newContent && viewer.value) {
    await loadSVG(newContent)
  }
})

// Watch for size changes
watch([() => props.width, () => props.height], ([newWidth, newHeight]) => {
  updateSize(newWidth, newHeight)
})

// Lifecycle hooks
onMounted(async () => {
  await nextTick()
  await initializeViewer()
})

onUnmounted(() => {
  if (viewer.value) {
    viewer.value.destroy()
  }
  if (controls.value) {
    controls.value.destroy()
  }
})

// Expose public methods
defineExpose({
  getViewer,
  getControls,
  loadSVG,
  resetView,
  startAnimation,
  stopAnimation,
  exportImage,
  updateSize
})
</script>

<style scoped>
.zdog-viewer-container {
  @apply relative w-full h-full flex;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.zdog-canvas {
  @apply flex-1 relative;
  min-height: 400px;
}

.zdog-controls-panel {
  @apply w-80 bg-white border-l border-gray-200 overflow-y-auto;
  flex-shrink: 0;
}

.zdog-controls-panel.controls-compact {
  @apply w-64;
}

.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-90 flex flex-col items-center justify-center z-10;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.loading-text {
  @apply text-gray-600 font-medium;
}

.error-overlay {
  @apply absolute inset-0 bg-red-50 bg-opacity-95 flex items-center justify-center z-20;
}

.error-content {
  @apply bg-white p-6 rounded-lg shadow-lg max-w-md mx-4 text-center;
}

.error-content h4 {
  @apply text-red-600 font-semibold text-lg mb-2;
}

.error-content p {
  @apply text-gray-700 mb-4;
}

.error-dismiss {
  @apply bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors;
}

/* Responsive design */
@media (max-width: 768px) {
  .zdog-viewer-container {
    @apply flex-col;
  }
  
  .zdog-controls-panel {
    @apply w-full h-auto border-l-0 border-t border-gray-200;
    max-height: 200px;
  }
  
  .zdog-controls-panel.controls-compact {
    @apply w-full;
  }
}
</style>
