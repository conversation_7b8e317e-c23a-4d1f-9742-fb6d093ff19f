# Zdog 3D SVG Viewer

A comprehensive TypeScript implementation of a 3D SVG viewer using the Zdog pseudo-3D engine. This viewer converts SVG graphics into interactive 3D visualizations with full type safety and modern web standards.

## Features

- **SVG Import & Parsing**: Full support for SVG path commands (M, L, C, Q, A, Z) with TypeScript type safety
- **3D Rendering**: Convert 2D SVG graphics to 3D shapes with configurable depth and extrusion
- **Interactive Controls**: Drag-to-rotate, zoom, pan, and keyboard navigation
- **Animation System**: Auto-rotation, smooth transitions, and shape animations
- **Export Functionality**: Export as PNG, SVG, WebP, or JPEG with customizable options
- **Performance Monitoring**: Real-time FPS tracking and render time metrics
- **Plugin System**: Extensible architecture for custom functionality
- **Responsive Design**: Automatic resizing and mobile-friendly touch controls

## Quick Start

### Basic Usage

```typescript
import { ZdogSVGViewer } from '@/viewers/zdog/ZdogViewer';

// Create viewer
const viewer = new ZdogSVGViewer({
  container: document.getElementById('viewer-container') as HTMLElement,
  width: 800,
  height: 600,
  dragRotate: true,
  autoResize: true
});

// Load SVG content
const svgContent = `
  <svg viewBox="0 0 100 100">
    <path d="M10,10 L90,10 L90,90 L10,90 Z" fill="#ff0000" />
    <circle cx="50" cy="50" r="20" fill="#0000ff" />
  </svg>
`;

await viewer.loadSVG(svgContent, {
  depth: 20,
  strokeWidth: 2,
  centerOrigin: true
});

// Start animation
viewer.startAnimation();
```

### Vue Component Integration

```vue
<template>
  <ZdogViewer
    :svg-content="svgContent"
    :width="800"
    :height="600"
    :show-controls="true"
    :auto-start="true"
    @viewer-ready="handleViewerReady"
    @svg-loaded="handleSvgLoaded"
  />
</template>

<script setup lang="ts">
import ZdogViewer from '@/components/ZdogViewer.vue';

const svgContent = ref('<svg>...</svg>');

const handleViewerReady = (viewer) => {
  console.log('Viewer ready:', viewer);
};

const handleSvgLoaded = (shapeCount) => {
  console.log('Loaded', shapeCount, 'shapes');
};
</script>
```

## API Reference

### ZdogSVGViewer Class

#### Constructor Options

```typescript
interface ZdogViewerOptions {
  container: HTMLElement;        // Required: Container element
  width?: number;               // Canvas width (default: container width)
  height?: number;              // Canvas height (default: container height)
  backgroundColor?: Color;      // Background color
  pixelRatio?: number;         // Device pixel ratio
  dragRotate?: boolean;        // Enable drag-to-rotate (default: true)
  autoResize?: boolean;        // Auto-resize on container change (default: true)
  zoom?: number;               // Initial zoom level (default: 1)
  onReady?: (viewer) => void;  // Ready callback
  onError?: (error) => void;   // Error callback
}
```

#### SVG Import Options

```typescript
interface SVGImportOptions {
  depth?: number;              // Extrusion depth (default: 10)
  strokeWidth?: number;        // Default stroke width (default: 2)
  preserveAspectRatio?: boolean; // Preserve SVG aspect ratio
  scale?: number;              // Scale factor (default: 1)
  centerOrigin?: boolean;      // Center at origin (default: true)
  flipY?: boolean;             // Flip Y axis (default: true)
  materialOptions?: MaterialOptions; // Material properties
}
```

#### Core Methods

```typescript
// Load SVG content
await viewer.loadSVG(svgContent: string, options?: SVGImportOptions): Promise<void>

// Animation control
viewer.startAnimation(): void
viewer.stopAnimation(): void

// View control
viewer.resetView(): void
viewer.setControls(controls: Partial<ViewerControls>): void

// Shape management
viewer.updateShape(id: string, properties: Partial<ShapeInfo>): void
viewer.removeShape(id: string): boolean
viewer.getAllShapes(): ShapeInfo[]

// Export functionality
await viewer.exportImage(options?: ExportOptions): Promise<Blob>

// Event handling
viewer.on(eventType: string, handler: Function): void
viewer.off(eventType: string, handler: Function): void

// Cleanup
viewer.destroy(): void
```

### Controls Interface

```typescript
interface ViewerControls {
  autoRotate: boolean;         // Auto-rotation enabled
  rotationSpeed: number;       // Rotation speed (rad/frame)
  zoom: number;               // Zoom level
  panX: number;               // Pan X offset
  panY: number;               // Pan Y offset
  rotationX: number;          // Manual rotation X
  rotationY: number;          // Manual rotation Y
  rotationZ: number;          // Manual rotation Z
  enableDrag: boolean;        // Drag interaction enabled
  enableZoom: boolean;        // Zoom interaction enabled
  enablePan: boolean;         // Pan interaction enabled
}
```

### Event System

```typescript
// Available events
viewer.on('load', (event) => {
  console.log('Loaded', event.data.shapeCount, 'shapes');
});

viewer.on('render', (event) => {
  console.log('FPS:', event.data.fps);
});

viewer.on('error', (event) => {
  console.error('Error:', event.data.error);
});

viewer.on('export', (event) => {
  console.log('Exported', event.data.format, 'file');
});
```

## Keyboard Controls

- **Arrow Keys**: Manual rotation
- **Space**: Toggle auto-rotation
- **R**: Reset view to default position

## Mouse/Touch Controls

- **Drag**: Rotate view
- **Wheel**: Zoom in/out
- **Touch**: Mobile-friendly drag and pinch gestures

## Export Options

```typescript
const blob = await viewer.exportImage({
  format: 'png',           // 'png', 'svg', 'webp', 'jpeg'
  quality: 0.9,           // Quality for lossy formats (0-1)
  width: 1920,            // Export width
  height: 1080,           // Export height
  backgroundColor: '#fff', // Background color
  transparent: false,      // Transparent background
  scale: 2                // Scale factor for high-DPI
});

// Download the exported image
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'export.png';
a.click();
```

## Performance Optimization

### Large Shape Counts

For SVGs with many shapes (>1000), consider:

```typescript
// Enable virtual rendering
const viewer = new ZdogSVGViewer({
  container,
  // ... other options
});

// Monitor performance
viewer.on('render', (event) => {
  if (event.data.fps < 30) {
    console.warn('Low FPS detected:', event.data.fps);
  }
});
```

### Memory Management

```typescript
// Clean up when done
viewer.destroy();

// Remove specific shapes to free memory
viewer.removeShape('shape-id');

// Clear all shapes
viewer.getAllShapes().forEach(shape => {
  viewer.removeShape(shape.id);
});
```

## Error Handling

```typescript
try {
  await viewer.loadSVG(svgContent);
} catch (error) {
  if (error instanceof SVGParseError) {
    console.error('SVG parsing failed:', error.message);
  } else if (error instanceof ZdogRenderError) {
    console.error('Rendering failed:', error.message);
  } else {
    console.error('Unknown error:', error);
  }
}
```

## Browser Support

- **Modern Browsers**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile**: iOS Safari 12+, Chrome Mobile 60+
- **Features**: Canvas 2D, SVG, ES2020, TypeScript

## Dependencies

- **zdog**: ^1.1.3 - Pseudo-3D engine
- **TypeScript**: ^5.0.0 - Type safety
- **Vue 3**: ^3.4.0 - Component framework (optional)

## License

MIT License - see LICENSE file for details.
