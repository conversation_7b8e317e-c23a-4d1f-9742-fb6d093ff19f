/**
 * TypeScript type definitions for Zdog pseudo-3D engine
 * Comprehensive type safety for all Zdog classes and interfaces
 */

declare module 'zdog' {
  // Core Vector interface
  export interface Vector {
    x: number;
    y: number;
    z: number;
  }

  // Color type - can be string or object
  export type Color = string | { r: number; g: number; b: number; a?: number };

  // Base shape options that all shapes inherit
  export interface ShapeOptions {
    addTo?: Anchor;
    translate?: Partial<Vector>;
    rotate?: Partial<Vector>;
    scale?: number | Partial<Vector>;
    color?: Color;
    stroke?: number;
    fill?: boolean;
    visible?: boolean;
    backface?: boolean | Color;
  }

  // Illustration options for the main canvas
  export interface IllustrationOptions extends ShapeOptions {
    element?: string | HTMLCanvasElement | HTMLSVGElement;
    width?: number;
    height?: number;
    zoom?: number;
    dragRotate?: boolean;
    resize?: boolean;
    onDragStart?: (pointer: Vector) => void;
    onDragMove?: (pointer: Vector, moveVector: Vector) => void;
    onDragEnd?: () => void;
    onResize?: (width: number, height: number) => void;
  }

  // Path point interface for complex paths
  export interface PathPoint {
    x?: number;
    y?: number;
    z?: number;
    arc?: Vector[];
    bezier?: Vector[];
    move?: Vector;
  }

  // Base Anchor class - parent for all shapes
  export class Anchor {
    constructor(options?: ShapeOptions);
    
    // Transform properties
    translate: Vector;
    rotate: Vector;
    scale: Vector;
    
    // Hierarchy
    addTo?: Anchor;
    children: Anchor[];
    
    // Methods
    addChild(child: Anchor): Anchor;
    removeChild(child: Anchor): Anchor;
    remove(): Anchor;
    copy(options?: Partial<ShapeOptions>): Anchor;
    copyGraph(options?: Partial<ShapeOptions>): Anchor;
    normalizeRotate(): void;
    updateGraph(): void;
    renderGraphCanvas(ctx: CanvasRenderingContext2D): void;
    renderGraphSvg(svg: SVGElement): void;
  }

  // Main Illustration class
  export class Illustration extends Anchor {
    constructor(options?: IllustrationOptions);
    
    element: HTMLCanvasElement | HTMLSVGElement;
    width: number;
    height: number;
    zoom: number;
    dragRotate: boolean;
    
    updateRenderGraph(): void;
    render(): void;
    setSize(width: number, height: number): void;
  }

  // Shape classes
  export interface RectOptions extends ShapeOptions {
    width?: number;
    height?: number;
  }

  export class Rect extends Anchor {
    constructor(options?: RectOptions);
    width: number;
    height: number;
  }

  export interface RoundedRectOptions extends RectOptions {
    cornerRadius?: number;
  }

  export class RoundedRect extends Anchor {
    constructor(options?: RoundedRectOptions);
    width: number;
    height: number;
    cornerRadius: number;
  }

  export interface EllipseOptions extends ShapeOptions {
    diameter?: number;
    width?: number;
    height?: number;
    quarters?: number;
  }

  export class Ellipse extends Anchor {
    constructor(options?: EllipseOptions);
    diameter: number;
    width: number;
    height: number;
    quarters: number;
  }

  export interface PolygonOptions extends ShapeOptions {
    radius?: number;
    sides?: number;
  }

  export class Polygon extends Anchor {
    constructor(options?: PolygonOptions);
    radius: number;
    sides: number;
  }

  export interface ShapePathOptions extends ShapeOptions {
    path?: (string | PathPoint)[];
    closed?: boolean;
  }

  export class Shape extends Anchor {
    constructor(options?: ShapePathOptions);
    path: (string | PathPoint)[];
    closed: boolean;
  }

  export interface GroupOptions extends ShapeOptions {
    updateSort?: boolean;
  }

  export class Group extends Anchor {
    constructor(options?: GroupOptions);
    updateSort: boolean;
  }

  // 3D shapes
  export interface CylinderOptions extends ShapeOptions {
    diameter?: number;
    length?: number;
  }

  export class Cylinder extends Anchor {
    constructor(options?: CylinderOptions);
    diameter: number;
    length: number;
  }

  export interface ConeOptions extends ShapeOptions {
    diameter?: number;
    length?: number;
  }

  export class Cone extends Anchor {
    constructor(options?: ConeOptions);
    diameter: number;
    length: number;
  }

  export interface BoxOptions extends ShapeOptions {
    width?: number;
    height?: number;
    depth?: number;
    frontFace?: Color;
    rearFace?: Color;
    leftFace?: Color;
    rightFace?: Color;
    topFace?: Color;
    bottomFace?: Color;
  }

  export class Box extends Anchor {
    constructor(options?: BoxOptions);
    width: number;
    height: number;
    depth: number;
    frontFace: Color;
    rearFace: Color;
    leftFace: Color;
    rightFace: Color;
    topFace: Color;
    bottomFace: Color;
  }

  export interface HemisphereOptions extends ShapeOptions {
    diameter?: number;
  }

  export class Hemisphere extends Anchor {
    constructor(options?: HemisphereOptions);
    diameter: number;
  }

  // Utility functions
  export const TAU: number;
  export function lerp(a: number, b: number, alpha: number): number;
  export function modulo(num: number, div: number): number;
  
  // Vector utilities
  export class Vector3d implements Vector {
    constructor(position?: Partial<Vector>);
    x: number;
    y: number;
    z: number;
    
    set(position: Partial<Vector>): Vector3d;
    copy(): Vector3d;
    add(vector: Vector): Vector3d;
    subtract(vector: Vector): Vector3d;
    multiply(vector: Vector | number): Vector3d;
    magnitude(): number;
    magnitude2d(): number;
    normalize(): Vector3d;
    cross(vector: Vector): Vector3d;
    dot(vector: Vector): number;
    lerp(vector: Vector, alpha: number): Vector3d;
  }

  // Export all classes as default
  export default {
    Illustration,
    Anchor,
    Shape,
    Group,
    Rect,
    RoundedRect,
    Ellipse,
    Polygon,
    Cylinder,
    Cone,
    Box,
    Hemisphere,
    Vector3d,
    TAU,
    lerp,
    modulo
  };
}
